<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <RootNamespace>VMSClient</RootNamespace>
    <AssemblyName>VMS-CLIENT</AssemblyName>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>VMS Client - Network Discovery</AssemblyTitle>
    <AssemblyDescription>VMS client with automatic server discovery and hybrid network support</AssemblyDescription>
    <AssemblyCompany>VMS Production Team</AssemblyCompany>
    <AssemblyProduct>VMS-CLIENT</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <Copyright>Copyright © 2025 VMS Production Team</Copyright>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    <ApplicationIcon>VMS-CLIENT.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <!-- Properly embed icon as resource for WPF -->
  <ItemGroup>
    <Resource Include="VMS-CLIENT.ico" />
  </ItemGroup>

</Project>
