{"version": 3, "targets": {"net8.0-windows7.0": {"Microsoft.NET.ILLink.Tasks/8.0.17": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}}, "net8.0-windows7.0/win-x64": {"Microsoft.NET.ILLink.Tasks/8.0.17": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}}}, "libraries": {"Microsoft.NET.ILLink.Tasks/8.0.17": {"sha512": "x5/y4l8AtshpBOrCZdlE4txw8K3e3s9meBFeZeR3l8hbbku2V7kK6ojhXvrbjg1rk3G+JqL1BI26gtgc1ZrdUw==", "type": "package", "path": "microsoft.net.illink.tasks/8.0.17", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.8.0.17.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net8.0/ILLink.Tasks.deps.json", "tools/net8.0/ILLink.Tasks.dll", "tools/net8.0/Mono.Cecil.Mdb.dll", "tools/net8.0/Mono.Cecil.Pdb.dll", "tools/net8.0/Mono.Cecil.Rocks.dll", "tools/net8.0/Mono.Cecil.dll", "tools/net8.0/Sdk/Sdk.props", "tools/net8.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net8.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net8.0/build/Microsoft.NET.ILLink.targets", "tools/net8.0/illink.deps.json", "tools/net8.0/illink.dll", "tools/net8.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["Microsoft.NET.ILLink.Tasks >= 8.0.17"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\VMS-SIMPLE-CLIENT\\VMS-SIMPLE-CLIENT.csproj", "projectName": "VMS-CLIENT-SIMPLE", "projectPath": "C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\VMS-SIMPLE-CLIENT\\VMS-SIMPLE-CLIENT.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\VMS-SIMPLE-CLIENT\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.17, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Crossgen2.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}