/**
 * VMS-ADMIN Service
 * Web-based administration dashboard service
 * Runs on port 8081 alongside VMS server (port 8080)
 */
declare module 'express-session' {
    interface SessionData {
        adminUser?: AdminUser;
    }
}
export interface AdminUser {
    id: number;
    username: string;
    role: string;
    department: string;
    permissions: string[];
    lastLogin: Date;
}
export interface DashboardStats {
    systemStatus: 'healthy' | 'degraded' | 'critical' | 'offline';
    vmsServer: {
        status: 'running' | 'stopped' | 'error';
        uptime: number;
        port: number;
        connections: number;
    };
    networkSystem: {
        mode: 'static' | 'dynamic' | 'hybrid';
        staticIP: string | null;
        dynamicIP: string | null;
        connectivity: boolean;
        lastChange: Date;
    };
    database: {
        status: 'connected' | 'disconnected' | 'error';
        size: number;
        lastBackup: Date | null;
    };
    users: {
        totalActive: number;
        adminSessions: number;
        departmentBreakdown: Record<string, number>;
    };
}
export declare class VMSAdminService {
    private _app;
    private _server;
    private _io;
    private _isRunning;
    private _port;
    private _connectedAdmins;
    constructor();
    /**
     * Setup Express middleware
     */
    private setupMiddleware;
    /**
     * Setup Express routes
     */
    private setupRoutes;
    /**
     * Setup Socket.IO handlers for real-time updates
     */
    private setupSocketHandlers;
    /**
     * Setup real-time updates from network services
     */
    private setupRealTimeUpdates;
    /**
     * Handle admin login
     */
    private handleLogin;
    /**
     * Handle admin logout
     */
    private handleLogout;
    /**
     * Handle authentication status check
     */
    private handleAuthStatus;
    /**
     * Require admin authentication middleware
     */
    private requireAdminAuth;
    /**
     * Get dashboard statistics
     */
    private getDashboardStats;
    /**
     * Collect comprehensive dashboard statistics
     */
    private collectDashboardStats;
    /**
     * Get database status information
     */
    private getDatabaseStatusInfo;
    /**
     * Get user statistics
     */
    private getUserStatistics;
    /**
     * Get system status
     */
    private getSystemStatus;
    /**
     * Get network status
     */
    private getNetworkStatus;
    /**
     * Restart VMS server
     */
    private restartVMSServer;
    /**
     * Switch network mode
     */
    private switchNetworkMode;
    /**
     * Trigger healing
     */
    private triggerHealing;
    /**
     * Get database status
     */
    private getDatabaseStatus;
    /**
     * Create database backup
     */
    private createDatabaseBackup;
    /**
     * List database backups
     */
    private listDatabaseBackups;
    /**
     * Get active users
     */
    private getActiveUsers;
    /**
     * Get user sessions
     */
    private getUserSessions;
    /**
     * Get recent logs
     */
    private getRecentLogs;
    /**
     * Download logs
     */
    private downloadLogs;
    /**
     * Start VMS-ADMIN service
     */
    start(): Promise<void>;
    /**
     * Stop VMS-ADMIN service
     */
    stop(): Promise<void>;
    /**
     * Get admin user by session ID
     */
    private getAdminUserBySession;
    /**
     * Check if service is running
     */
    isRunning(): boolean;
    /**
     * Get service port
     */
    getPort(): number;
}
export declare const vmsAdminService: VMSAdminService;
