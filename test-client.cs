using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;

class TestClient
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🔍 Testing VMS Server Connection...");
        
        try
        {
            // Test 1: Check if server is responding
            using (var client = new HttpClient())
            {
                client.Timeout = TimeSpan.FromSeconds(5);
                var response = await client.GetAsync("http://localhost:8080");
                Console.WriteLine($"✅ Server Response: {response.StatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✅ VMS Server is accessible");
                    
                    // Test 2: Try to launch browser
                    Console.WriteLine("🚀 Attempting to launch browser...");
                    
                    var processInfo = new ProcessStartInfo
                    {
                        FileName = "http://localhost:8080",
                        UseShellExecute = true
                    };
                    
                    var process = Process.Start(processInfo);
                    
                    if (process != null)
                    {
                        Console.WriteLine("✅ Browser launched successfully!");
                        Console.WriteLine("Press any key to exit...");
                        Console.ReadKey();
                    }
                    else
                    {
                        Console.WriteLine("❌ Failed to launch browser");
                    }
                }
                else
                {
                    Console.WriteLine($"❌ Server returned error: {response.StatusCode}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
