using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;

namespace VMSClient
{
    /// <summary>
    /// Network Discovery Client for VMS Production Server Auto-Discovery
    /// Handles dynamic IP discovery and automatic server connection
    /// </summary>
    public class NetworkDiscoveryClient
    {
        private UdpClient _discoveryClient;
        private UdpClient _broadcastListener;
        private Timer _discoveryTimer;
        private bool _isRunning = false;
        private readonly object _lockObject = new object();

        public event EventHandler<ServerDiscoveredEventArgs> ServerDiscovered;
        public event EventHandler<string> LogMessage;

        private readonly NetworkDiscoveryConfig _config = new NetworkDiscoveryConfig
        {
            BroadcastPort = 8081,
            DiscoveryPort = 8082,
            DiscoveryInterval = 10000, // 10 seconds
            DiscoveryTimeout = 5000,   // 5 seconds
            MaxRetries = 3
        };

        private readonly List<DiscoveredServer> _discoveredServers = new List<DiscoveredServer>();

        /// <summary>
        /// Start network discovery
        /// </summary>
        public async Task StartDiscoveryAsync()
        {
            try
            {
                if (_isRunning)
                {
                    LogMessage?.Invoke(this, "Network discovery is already running");
                    return;
                }

                LogMessage?.Invoke(this, "🌐 Starting VMS server discovery...");

                // Start listening for server announcements
                await StartBroadcastListenerAsync();

                // Start active discovery
                await StartActiveDiscoveryAsync();

                // Schedule periodic discovery
                _discoveryTimer = new Timer(PeriodicDiscovery, null, 
                    TimeSpan.FromSeconds(2), TimeSpan.FromMilliseconds(_config.DiscoveryInterval));

                _isRunning = true;
                LogMessage?.Invoke(this, "✅ Network discovery started successfully");
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"❌ Failed to start network discovery: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Stop network discovery
        /// </summary>
        public void StopDiscovery()
        {
            lock (_lockObject)
            {
                if (!_isRunning) return;

                _isRunning = false;
                
                _discoveryTimer?.Dispose();
                _discoveryTimer = null;

                _broadcastListener?.Close();
                _broadcastListener?.Dispose();
                _broadcastListener = null;

                _discoveryClient?.Close();
                _discoveryClient?.Dispose();
                _discoveryClient = null;

                LogMessage?.Invoke(this, "🛑 Network discovery stopped");
            }
        }

        /// <summary>
        /// Get list of discovered servers
        /// </summary>
        public List<DiscoveredServer> GetDiscoveredServers()
        {
            lock (_lockObject)
            {
                // Remove stale servers (older than 60 seconds)
                var cutoff = DateTime.UtcNow.AddSeconds(-60);
                _discoveredServers.RemoveAll(s => s.LastSeen < cutoff);
                
                return new List<DiscoveredServer>(_discoveredServers);
            }
        }

        /// <summary>
        /// Check if discovery is running
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// Start listening for server broadcast announcements
        /// </summary>
        private async Task StartBroadcastListenerAsync()
        {
            try
            {
                _broadcastListener = new UdpClient(_config.BroadcastPort);
                _broadcastListener.EnableBroadcast = true;

                LogMessage?.Invoke(this, $"📡 Listening for server announcements on port {_config.BroadcastPort}");

                // Start listening in background
                _ = Task.Run(async () =>
                {
                    while (_isRunning && _broadcastListener != null)
                    {
                        try
                        {
                            var result = await _broadcastListener.ReceiveAsync();
                            await ProcessServerAnnouncementAsync(result.Buffer, result.RemoteEndPoint);
                        }
                        catch (ObjectDisposedException)
                        {
                            // Expected when stopping
                            break;
                        }
                        catch (Exception ex)
                        {
                            LogMessage?.Invoke(this, $"⚠️ Error receiving broadcast: {ex.Message}");
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"❌ Failed to start broadcast listener: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Start active discovery by sending requests
        /// </summary>
        private async Task StartActiveDiscoveryAsync()
        {
            try
            {
                _discoveryClient = new UdpClient();
                _discoveryClient.EnableBroadcast = true;

                LogMessage?.Invoke(this, "🔍 Starting active server discovery");
                await SendDiscoveryRequestAsync();
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"❌ Failed to start active discovery: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Send discovery request to network
        /// </summary>
        private async Task SendDiscoveryRequestAsync()
        {
            try
            {
                var request = new
                {
                    type = "VMS_DISCOVERY_REQUEST",
                    clientId = Environment.MachineName,
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
                };

                var requestJson = JsonSerializer.Serialize(request);
                var requestBytes = Encoding.UTF8.GetBytes(requestJson);

                // Send to broadcast addresses
                var broadcastAddresses = GetBroadcastAddresses();
                
                foreach (var address in broadcastAddresses)
                {
                    try
                    {
                        await _discoveryClient.SendAsync(requestBytes, requestBytes.Length, 
                            new IPEndPoint(IPAddress.Parse(address), _config.DiscoveryPort));
                        
                        LogMessage?.Invoke(this, $"📤 Discovery request sent to {address}:{_config.DiscoveryPort}");
                    }
                    catch (Exception ex)
                    {
                        LogMessage?.Invoke(this, $"⚠️ Failed to send discovery to {address}: {ex.Message}");
                    }
                }

                // Listen for responses
                _ = Task.Run(async () =>
                {
                    var timeout = DateTime.UtcNow.AddMilliseconds(_config.DiscoveryTimeout);
                    
                    while (DateTime.UtcNow < timeout && _isRunning)
                    {
                        try
                        {
                            var result = await _discoveryClient.ReceiveAsync();
                            await ProcessDiscoveryResponseAsync(result.Buffer, result.RemoteEndPoint);
                        }
                        catch (Exception ex)
                        {
                            LogMessage?.Invoke(this, $"⚠️ Error receiving discovery response: {ex.Message}");
                            break;
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"❌ Error sending discovery request: {ex.Message}");
            }
        }

        /// <summary>
        /// Process server announcement from broadcast
        /// </summary>
        private async Task ProcessServerAnnouncementAsync(byte[] data, IPEndPoint remoteEndPoint)
        {
            try
            {
                var json = Encoding.UTF8.GetString(data);
                var announcement = JsonSerializer.Deserialize<JsonElement>(json);

                if (announcement.TryGetProperty("type", out var typeElement) && 
                    typeElement.GetString() == "VMS_SERVICE_ANNOUNCEMENT")
                {
                    LogMessage?.Invoke(this, $"📡 Server announcement received from {remoteEndPoint.Address}");
                    
                    if (announcement.TryGetProperty("service", out var serviceElement))
                    {
                        var server = ParseServerInfo(serviceElement, remoteEndPoint.Address.ToString());
                        if (server != null)
                        {
                            AddOrUpdateServer(server);
                            ServerDiscovered?.Invoke(this, new ServerDiscoveredEventArgs(server));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error processing server announcement: {ex.Message}");
            }
        }

        /// <summary>
        /// Process discovery response from server
        /// </summary>
        private async Task ProcessDiscoveryResponseAsync(byte[] data, IPEndPoint remoteEndPoint)
        {
            try
            {
                var json = Encoding.UTF8.GetString(data);
                var response = JsonSerializer.Deserialize<JsonElement>(json);

                if (response.TryGetProperty("type", out var typeElement) && 
                    typeElement.GetString() == "VMS_DISCOVERY_RESPONSE")
                {
                    LogMessage?.Invoke(this, $"📥 Discovery response received from {remoteEndPoint.Address}");
                    
                    if (response.TryGetProperty("server", out var serverElement))
                    {
                        var server = ParseServerInfo(serverElement, remoteEndPoint.Address.ToString());
                        if (server != null)
                        {
                            AddOrUpdateServer(server);
                            ServerDiscovered?.Invoke(this, new ServerDiscoveredEventArgs(server));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error processing discovery response: {ex.Message}");
            }
        }

        /// <summary>
        /// Parse server information from JSON
        /// </summary>
        private DiscoveredServer ParseServerInfo(JsonElement serverElement, string serverIP)
        {
            try
            {
                var serviceName = serverElement.TryGetProperty("serviceName", out var nameElement) ? 
                    nameElement.GetString() : "Unknown";
                var serverPort = serverElement.TryGetProperty("serverPort", out var portElement) ? 
                    portElement.GetInt32() : 8080;
                var version = serverElement.TryGetProperty("version", out var versionElement) ? 
                    versionElement.GetString() : "Unknown";

                return new DiscoveredServer
                {
                    ServiceName = serviceName,
                    ServerIP = serverIP,
                    ServerPort = serverPort,
                    Version = version,
                    LastSeen = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error parsing server info: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Add or update server in discovered list
        /// </summary>
        private void AddOrUpdateServer(DiscoveredServer server)
        {
            lock (_lockObject)
            {
                var existing = _discoveredServers.FirstOrDefault(s => 
                    s.ServerIP == server.ServerIP && s.ServerPort == server.ServerPort);

                if (existing != null)
                {
                    existing.LastSeen = server.LastSeen;
                    existing.Version = server.Version;
                }
                else
                {
                    _discoveredServers.Add(server);
                    LogMessage?.Invoke(this, $"✅ New VMS server discovered: {server.ServerIP}:{server.ServerPort}");
                }
            }
        }

        /// <summary>
        /// Periodic discovery callback
        /// </summary>
        private async void PeriodicDiscovery(object state)
        {
            if (!_isRunning) return;

            try
            {
                await SendDiscoveryRequestAsync();
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Periodic discovery error: {ex.Message}");
            }
        }

        /// <summary>
        /// Get broadcast addresses for all network interfaces
        /// </summary>
        private List<string> GetBroadcastAddresses()
        {
            var addresses = new List<string>();
            
            try
            {
                // Add common broadcast addresses
                addresses.Add("***************"); // Global broadcast
                addresses.Add("*************");   // Common home network
                addresses.Add("*************");   // Common home network
                addresses.Add("**************");  // Office network
                addresses.Add("**********");      // Corporate network
                
                // Add network-specific broadcasts
                var networkInterfaces = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces();
                foreach (var ni in networkInterfaces)
                {
                    if (ni.OperationalStatus == System.Net.NetworkInformation.OperationalStatus.Up &&
                        ni.NetworkInterfaceType != System.Net.NetworkInformation.NetworkInterfaceType.Loopback)
                    {
                        var ipProps = ni.GetIPProperties();
                        foreach (var addr in ipProps.UnicastAddresses)
                        {
                            if (addr.Address.AddressFamily == AddressFamily.InterNetwork)
                            {
                                var ip = addr.Address.GetAddressBytes();
                                var mask = addr.IPv4Mask.GetAddressBytes();
                                
                                // Calculate broadcast address
                                var broadcast = new byte[4];
                                for (int i = 0; i < 4; i++)
                                {
                                    broadcast[i] = (byte)(ip[i] | (~mask[i] & 0xFF));
                                }
                                
                                var broadcastAddr = new IPAddress(broadcast).ToString();
                                if (!addresses.Contains(broadcastAddr))
                                {
                                    addresses.Add(broadcastAddr);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage?.Invoke(this, $"⚠️ Error getting broadcast addresses: {ex.Message}");
            }

            return addresses;
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            StopDiscovery();
        }
    }

    /// <summary>
    /// Network discovery configuration
    /// </summary>
    public class NetworkDiscoveryConfig
    {
        public int BroadcastPort { get; set; }
        public int DiscoveryPort { get; set; }
        public int DiscoveryInterval { get; set; }
        public int DiscoveryTimeout { get; set; }
        public int MaxRetries { get; set; }
    }

    /// <summary>
    /// Discovered server information
    /// </summary>
    public class DiscoveredServer
    {
        public string ServiceName { get; set; }
        public string ServerIP { get; set; }
        public int ServerPort { get; set; }
        public string Version { get; set; }
        public DateTime LastSeen { get; set; }
    }

    /// <summary>
    /// Server discovered event arguments
    /// </summary>
    public class ServerDiscoveredEventArgs : EventArgs
    {
        public DiscoveredServer Server { get; }

        public ServerDiscoveredEventArgs(DiscoveredServer server)
        {
            Server = server;
        }
    }
}
