using System;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace VMSClient
{
    public partial class SimpleVMSClient : Form
    {
        private Button connectButton;
        private Label statusLabel;
        private Label serverLabel;
        private ProgressBar progressBar;
        private HttpClient httpClient;
        private string serverUrl = "";

        public SimpleVMSClient()
        {
            InitializeComponent();
            httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(5);
            StartServerDetection();
        }

        private void InitializeComponent()
        {
            this.Text = "VMS CLIENT - Development";
            this.Size = new System.Drawing.Size(500, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // Status Label
            statusLabel = new Label();
            statusLabel.Text = "🔍 Searching for VMS server...";
            statusLabel.Location = new System.Drawing.Point(20, 20);
            statusLabel.Size = new System.Drawing.Size(450, 30);
            statusLabel.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            statusLabel.ForeColor = System.Drawing.Color.Orange;
            this.Controls.Add(statusLabel);

            // Server Label
            serverLabel = new Label();
            serverLabel.Text = "Server: Not detected";
            serverLabel.Location = new System.Drawing.Point(20, 60);
            serverLabel.Size = new System.Drawing.Size(450, 25);
            serverLabel.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.Controls.Add(serverLabel);

            // Progress Bar
            progressBar = new ProgressBar();
            progressBar.Location = new System.Drawing.Point(20, 100);
            progressBar.Size = new System.Drawing.Size(450, 20);
            progressBar.Style = ProgressBarStyle.Marquee;
            this.Controls.Add(progressBar);

            // Connect Button
            connectButton = new Button();
            connectButton.Text = "Connect to VMS";
            connectButton.Location = new System.Drawing.Point(150, 150);
            connectButton.Size = new System.Drawing.Size(200, 50);
            connectButton.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            connectButton.BackColor = System.Drawing.Color.FromArgb(21, 101, 192);
            connectButton.ForeColor = System.Drawing.Color.White;
            connectButton.FlatStyle = FlatStyle.Flat;
            connectButton.Enabled = false;
            connectButton.Click += ConnectButton_Click;
            this.Controls.Add(connectButton);

            // Refresh Button
            var refreshButton = new Button();
            refreshButton.Text = "Refresh";
            refreshButton.Location = new System.Drawing.Point(200, 220);
            refreshButton.Size = new System.Drawing.Size(100, 30);
            refreshButton.Click += (s, e) => StartServerDetection();
            this.Controls.Add(refreshButton);
        }

        private async void StartServerDetection()
        {
            UpdateStatus("🔍 Searching for VMS server...", System.Drawing.Color.Orange);
            serverLabel.Text = "Server: Scanning...";
            connectButton.Enabled = false;
            progressBar.Style = ProgressBarStyle.Marquee;

            // Try common localhost ports first
            string[] testUrls = {
                "http://localhost:8080",
                "http://127.0.0.1:8080",
                "http://localhost:3000",
                "http://localhost:8081",
                "http://localhost:9000"
            };

            foreach (string url in testUrls)
            {
                try
                {
                    var response = await httpClient.GetAsync(url);
                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        if (content.Contains("VMS") || content.Contains("Voucher") || 
                            response.Headers.Contains("X-VMS-Server") || 
                            url.Contains("8080")) // Assume 8080 is VMS
                        {
                            serverUrl = url;
                            UpdateStatus("✅ VMS server found!", System.Drawing.Color.Green);
                            serverLabel.Text = $"Server: {url}";
                            connectButton.Enabled = true;
                            connectButton.BackColor = System.Drawing.Color.FromArgb(76, 175, 80);
                            connectButton.Text = "🚀 Connect to VMS";
                            progressBar.Style = ProgressBarStyle.Blocks;
                            progressBar.Value = 100;
                            return;
                        }
                    }
                }
                catch
                {
                    // Continue to next URL
                }
            }

            // If localhost fails, try network scanning
            await TryNetworkScan();
        }

        private async Task TryNetworkScan()
        {
            UpdateStatus("🌐 Scanning local network...", System.Drawing.Color.Orange);
            
            try
            {
                // Get local IP and scan same subnet
                var localIP = GetLocalIPAddress();
                if (!string.IsNullOrEmpty(localIP))
                {
                    var subnet = localIP.Substring(0, localIP.LastIndexOf('.'));
                    
                    // Try a few common IPs in the subnet
                    for (int i = 1; i <= 10; i++)
                    {
                        string testIP = $"{subnet}.{i}";
                        string testUrl = $"http://{testIP}:8080";
                        
                        try
                        {
                            var response = await httpClient.GetAsync(testUrl);
                            if (response.IsSuccessStatusCode)
                            {
                                serverUrl = testUrl;
                                UpdateStatus("✅ VMS server found on network!", System.Drawing.Color.Green);
                                serverLabel.Text = $"Server: {testUrl}";
                                connectButton.Enabled = true;
                                connectButton.BackColor = System.Drawing.Color.FromArgb(76, 175, 80);
                                connectButton.Text = "🚀 Connect to VMS";
                                progressBar.Style = ProgressBarStyle.Blocks;
                                progressBar.Value = 100;
                                return;
                            }
                        }
                        catch
                        {
                            // Continue scanning
                        }
                    }
                }
            }
            catch
            {
                // Network scan failed
            }

            // No server found
            UpdateStatus("❌ No VMS server found", System.Drawing.Color.Red);
            serverLabel.Text = "Server: Not found";
            progressBar.Style = ProgressBarStyle.Blocks;
            progressBar.Value = 0;
        }

        private string GetLocalIPAddress()
        {
            try
            {
                var host = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                    {
                        return ip.ToString();
                    }
                }
            }
            catch { }
            return "";
        }

        private void UpdateStatus(string message, System.Drawing.Color color)
        {
            if (statusLabel.InvokeRequired)
            {
                statusLabel.Invoke(new Action(() => {
                    statusLabel.Text = message;
                    statusLabel.ForeColor = color;
                }));
            }
            else
            {
                statusLabel.Text = message;
                statusLabel.ForeColor = color;
            }
        }

        private async void ConnectButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(serverUrl))
            {
                MessageBox.Show("No VMS server detected. Please try refreshing.", "Connection Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                UpdateStatus("🚀 Opening VMS system...", System.Drawing.Color.Blue);
                
                Process.Start(new ProcessStartInfo
                {
                    FileName = serverUrl,
                    UseShellExecute = true
                });

                MessageBox.Show($"VMS system opened successfully!\n\nServer: {serverUrl}\n\nThe VMS CLIENT will now close.",
                    "VMS CLIENT - Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open VMS system:\n\n{ex.Message}", "Connection Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus("❌ Failed to open browser", System.Drawing.Color.Red);
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            httpClient?.Dispose();
            base.OnFormClosed(e);
        }
    }

    class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new SimpleVMSClient());
        }
    }
}
