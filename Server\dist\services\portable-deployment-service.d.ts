/**
 * Portable Deployment Service
 * Handles dynamic path resolution and configuration generation for unknown deployment paths
 * Enables VMS to run from any location without hardcoded paths
 */
export interface DeploymentPaths {
    vmsRoot: string;
    serverRoot: string;
    clientRoot: string;
    toolsRoot: string;
    logsRoot: string;
    configRoot: string;
    databasePath: string;
    backupRoot: string;
}
export interface DeploymentConfig {
    deploymentId: string;
    deploymentTime: Date;
    deploymentMode: 'automated' | 'manual' | 'service';
    serverIP: string;
    serverPort: number;
    paths: DeploymentPaths;
    systemInfo: {
        platform: string;
        hostname: string;
        username: string;
        nodeVersion: string;
    };
}
export declare class PortableDeploymentService {
    private _deploymentConfig;
    private _isInitialized;
    /**
     * Initialize portable deployment system
     */
    initialize(): Promise<DeploymentConfig>;
    /**
     * Detect deployment paths dynamically
     */
    private detectDeploymentPaths;
    /**
     * Find VMS root directory by traversing up from a starting directory
     */
    private findVMSRoot;
    /**
     * Check if directory is VMS root
     */
    private isVMSRoot;
    /**
     * Search common VMS installation locations
     */
    private searchCommonLocations;
    /**
     * Get available Windows drives
     */
    private getWindowsDrives;
    /**
     * Validate that critical paths exist
     */
    private validatePaths;
    /**
     * Ensure all required directories exist
     */
    private ensureDirectoriesExist;
    /**
     * Generate configuration files with dynamic paths
     */
    private generateConfigurationFiles;
    /**
     * Generate relative paths for portability
     */
    private generateRelativePaths;
    /**
     * Update environment variables for current process
     */
    private updateEnvironmentVariables;
    /**
     * Detect deployment mode
     */
    private detectDeploymentMode;
    /**
     * Detect server IP address
     */
    private detectServerIP;
    /**
     * Get system information
     */
    private getSystemInfo;
    /**
     * Generate unique deployment ID
     */
    private generateDeploymentId;
    /**
     * Get deployment configuration
     */
    getDeploymentConfig(): DeploymentConfig | null;
    /**
     * Get deployment paths
     */
    getDeploymentPaths(): DeploymentPaths | null;
    /**
     * Check if service is initialized
     */
    isInitialized(): boolean;
    /**
     * Resolve path relative to VMS root
     */
    resolvePath(...pathSegments: string[]): string;
    /**
     * Get absolute path for a relative path
     */
    getAbsolutePath(relativePath: string): string;
}
export declare const portableDeploymentService: PortableDeploymentService;
