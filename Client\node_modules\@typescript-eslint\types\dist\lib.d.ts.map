{"version": 3, "file": "lib.d.ts", "sourceRoot": "", "sources": ["../src/lib.ts"], "names": [], "mappings": "AAKA,KAAK,GAAG,GACJ,YAAY,GACZ,mBAAmB,GACnB,KAAK,GACL,mBAAmB,GACnB,cAAc,GACd,KAAK,GACL,KAAK,GACL,KAAK,GACL,QAAQ,GACR,mBAAmB,GACnB,aAAa,GACb,kBAAkB,GAClB,iBAAiB,GACjB,gBAAgB,GAChB,cAAc,GACd,gBAAgB,GAChB,eAAe,GACf,yBAAyB,GACzB,QAAQ,GACR,sBAAsB,GACtB,aAAa,GACb,aAAa,GACb,QAAQ,GACR,aAAa,GACb,aAAa,GACb,aAAa,GACb,eAAe,GACf,qBAAqB,GACrB,eAAe,GACf,oBAAoB,GACpB,QAAQ,GACR,uBAAuB,GACvB,sBAAsB,GACtB,aAAa,GACb,aAAa,GACb,gBAAgB,GAChB,eAAe,GACf,QAAQ,GACR,cAAc,GACd,aAAa,GACb,aAAa,GACb,eAAe,GACf,eAAe,GACf,eAAe,GACf,QAAQ,GACR,eAAe,GACf,aAAa,GACb,aAAa,GACb,aAAa,GACb,eAAe,GACf,gBAAgB,GAChB,qBAAqB,GACrB,eAAe,GACf,yBAAyB,GACzB,QAAQ,GACR,aAAa,GACb,aAAa,GACb,gBAAgB,GAChB,eAAe,GACf,gBAAgB,GAChB,QAAQ,GACR,cAAc,GACd,cAAc,GACd,aAAa,GACb,aAAa,GACb,eAAe,GACf,eAAe,GACf,qBAAqB,GACrB,eAAe,GACf,QAAQ,GACR,cAAc,GACd,mBAAmB,GACnB,aAAa,GACb,aAAa,GACb,QAAQ,GACR,cAAc,GACd,sBAAsB,GACtB,eAAe,GACf,mBAAmB,GACnB,mBAAmB,GACnB,mBAAmB,GACnB,aAAa,GACb,aAAa,GACb,iBAAiB,GACjB,eAAe,GACf,gBAAgB,GAChB,eAAe,GACf,eAAe,GACf,eAAe,GACf,gBAAgB,GAChB,KAAK,GACL,YAAY,GACZ,WAAW,GACX,yBAAyB,GACzB,yBAAyB,GACzB,oBAAoB,CAAC;AAEzB,OAAO,EAAE,GAAG,EAAE,CAAC"}