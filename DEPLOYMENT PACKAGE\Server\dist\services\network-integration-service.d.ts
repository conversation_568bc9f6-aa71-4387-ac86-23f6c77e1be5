/**
 * Network Integration Service
 * Orchestrates all network services for bulletproof deployment
 * Provides unified interface for hybrid network management
 */
import { EventEmitter } from 'events';
export interface NetworkSystemStatus {
    overall: 'healthy' | 'degraded' | 'critical' | 'offline';
    components: {
        hybridNetwork: boolean;
        intelligentFallback: boolean;
        selfHealing: boolean;
        networkDiscovery: boolean;
        portableDeployment: boolean;
        vmsAdmin: boolean;
    };
    currentMode: 'static' | 'dynamic' | 'hybrid';
    serverURL: string;
    lastUpdate: Date;
    issues: string[];
    recommendations: string[];
}
export declare class NetworkIntegrationService extends EventEmitter {
    private _isInitialized;
    private _isRunning;
    private _startupTime;
    constructor();
    /**
     * Initialize and start all network services
     */
    initialize(): Promise<void>;
    /**
     * Gracefully shutdown all network services
     */
    shutdown(): Promise<void>;
    /**
     * Setup event listeners for service integration
     */
    private setupEventListeners;
    /**
     * Get comprehensive system status
     */
    getSystemStatus(): Promise<NetworkSystemStatus>;
    /**
     * Get comprehensive system statistics
     */
    getSystemStats(): Promise<any>;
    /**
     * Force network mode switch
     */
    switchNetworkMode(mode: 'static' | 'dynamic'): Promise<boolean>;
    /**
     * Trigger manual healing
     */
    triggerHealing(): Promise<boolean>;
    /**
     * Restart all network services
     */
    restartServices(): Promise<boolean>;
    /**
     * Get current server URL
     */
    getServerURL(): string;
    /**
     * Check if system is ready
     */
    isReady(): boolean;
    /**
     * Get deployment information
     */
    getDeploymentInfo(): any;
    /**
     * Export system configuration for backup/restore
     */
    exportConfiguration(): Promise<any>;
}
export declare const networkIntegrationService: NetworkIntegrationService;
