{"version": 3, "file": "ScopeManager.d.ts", "sourceRoot": "", "sources": ["../src/ScopeManager.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAErE,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AACrC,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAG3C,OAAO,EACL,UAAU,EACV,UAAU,EACV,UAAU,EACV,oBAAoB,EACpB,QAAQ,EACR,2BAA2B,EAC3B,aAAa,EACb,iBAAiB,EACjB,WAAW,EACX,eAAe,EACf,WAAW,EAEX,WAAW,EACX,WAAW,EACX,aAAa,EACb,SAAS,EACT,SAAS,EACV,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,0BAA0B,EAAE,MAAM,oCAAoC,CAAC;AAChF,OAAO,EAAE,qBAAqB,EAAE,MAAM,+BAA+B,CAAC;AAEtE,UAAU,mBAAmB;IAC3B,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,UAAU,CAAC,EAAE,UAAU,CAAC;CACzB;AAED;;GAEG;AACH,cAAM,YAAY;;IAGT,YAAY,EAAE,KAAK,GAAG,IAAI,CAAC;IAElC,SAAgB,iBAAiB,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;IAEtE;;OAEG;IACI,WAAW,EAAE,WAAW,GAAG,IAAI,CAAC;IAEvC,SAAgB,WAAW,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IAE7D;;;OAGG;IACH,SAAgB,MAAM,EAAE,KAAK,EAAE,CAAC;gBAEpB,OAAO,EAAE,mBAAmB;IASjC,KAAK,IAAI,OAAO;IAIhB,cAAc,IAAI,OAAO;IAIzB,eAAe,IAAI,OAAO;IAI1B,QAAQ,IAAI,OAAO;IAInB,qBAAqB,IAAI,OAAO;IAIvC,IAAW,SAAS,IAAI,QAAQ,EAAE,CAQjC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,EAAE;IAI5D;;;;;;;OAOG;IACI,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,UAAQ,GAAG,KAAK,GAAG,IAAI;IAoCzD,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU;IAKrD,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU;IAKrD,8BAA8B,CACnC,IAAI,EAAE,0BAA0B,CAAC,OAAO,CAAC,GACxC,0BAA0B;IAOtB,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,UAAU;IAKrD,yBAAyB,CAC9B,IAAI,EAAE,qBAAqB,CAAC,OAAO,CAAC,GACnC,qBAAqB;IAOjB,wBAAwB,CAC7B,IAAI,EAAE,oBAAoB,CAAC,OAAO,CAAC,GAClC,oBAAoB;IAOhB,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,QAAQ;IAK/C,+BAA+B,CACpC,IAAI,EAAE,2BAA2B,CAAC,OAAO,CAAC,GACzC,2BAA2B;IAOvB,iBAAiB,CACtB,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,EAC5B,kBAAkB,EAAE,OAAO,GAC1B,aAAa;IAOT,qBAAqB,CAC1B,IAAI,EAAE,iBAAiB,CAAC,OAAO,CAAC,GAC/B,iBAAiB;IAKb,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW;IAIxD,mBAAmB,CAAC,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,eAAe;IAKpE,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW;IAKxD,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW;IAKxD,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,WAAW;IAKxD,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,aAAa;IAK9D,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,SAAS;IAKlD,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,GAAG,SAAS;IAOzD,SAAS,CAAC,SAAS,CAAC,CAAC,SAAS,KAAK,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC;CASlD;AAED,OAAO,EAAE,YAAY,EAAE,CAAC"}