{"version": 3, "file": "getParsedConfigFile.js", "sourceRoot": "", "sources": ["../../src/create-program/getParsedConfigFile.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAgES,kDAAmB;AA9D5B,4CAA8B;AAC9B,gDAAkC;AAElC,qCAAiD;AAEjD;;;;;GAKG;AACH,SAAS,mBAAmB,CAC1B,QAAmB,EACnB,UAAkB,EAClB,gBAAyB;IAEzB,uEAAuE;IACvE,IAAI,QAAQ,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CACb,qEAAqE,CACtE,CAAC;IACJ,CAAC;IAED,MAAM,MAAM,GAAG,QAAQ,CAAC,gCAAgC,CACtD,UAAU,EACV,8BAAqB,EACrB;QACE,UAAU,EAAE,EAAE,CAAC,UAAU;QACzB,mBAAmB;QACnB,mCAAmC,EAAE,IAAI,CAAC,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,oCAAoC;QAClF,CAAC;QACD,aAAa,EAAE,QAAQ,CAAC,GAAG,CAAC,aAAa;QACzC,QAAQ,EAAE,IAAI,CAAC,EAAE,CACf,EAAE,CAAC,YAAY,CACb,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,IAAI,CAAC,EACrE,OAAO,CACR;QACH,yBAAyB,EAAE,QAAQ,CAAC,GAAG,CAAC,yBAAyB;KAClE,CACF,CAAC;IAEF,IAAI,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,oEAAoE;IACpE,OAAO,MAAO,CAAC;IAEf,SAAS,mBAAmB;QAC1B,OAAO,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IAC3E,CAAC;IAED,SAAS,iBAAiB,CAAC,WAA4B;QACrD,OAAO,QAAQ,CAAC,iBAAiB,CAAC,WAAW,EAAE;YAC7C,oBAAoB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B,mBAAmB;YACnB,UAAU,EAAE,GAAG,EAAE,CAAC,IAAI;SACvB,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}