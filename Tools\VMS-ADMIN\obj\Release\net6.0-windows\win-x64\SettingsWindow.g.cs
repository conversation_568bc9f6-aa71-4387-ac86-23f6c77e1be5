﻿#pragma checksum "..\..\..\..\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "182613E019595343FD4DAA8E506B5AAC2E7BF7AD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace VMSADMIN {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 84 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscoveryTimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RefreshIntervalTextBox;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoConnectCheckBox;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ManualServerIPTextBox;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AdminPortTextBox;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConnectionTimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MinimizeToTrayCheckBox;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartWithWindowsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoCloseAfterConnectCheckBox;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LogLevelComboBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableLoggingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenLogFolderButton;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearLogsButton;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/VMS-ADMIN;V1.0.0.0;component/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DiscoveryTimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.RefreshIntervalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.AutoConnectCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 4:
            this.ManualServerIPTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.AdminPortTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.ConnectionTimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.MinimizeToTrayCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.StartWithWindowsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.ShowNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.AutoCloseAfterConnectCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.LogLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.EnableLoggingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.OpenLogFolderButton = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\SettingsWindow.xaml"
            this.OpenLogFolderButton.Click += new System.Windows.RoutedEventHandler(this.OpenLogFolderButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ClearLogsButton = ((System.Windows.Controls.Button)(target));
            
            #line 150 "..\..\..\..\SettingsWindow.xaml"
            this.ClearLogsButton.Click += new System.Windows.RoutedEventHandler(this.ClearLogsButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\SettingsWindow.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 163 "..\..\..\..\SettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\SettingsWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

