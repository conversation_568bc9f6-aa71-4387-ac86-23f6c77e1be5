/**
 * Hybrid Network Management Service
 * Combines static IP assignment with network discovery backup
 * Provides bulletproof network handling for production deployment
 */
import { EventEmitter } from 'events';
export interface NetworkConfiguration {
    mode: 'static' | 'dynamic' | 'hybrid';
    staticIP: string | null;
    dynamicIP: string | null;
    currentIP: string;
    port: number;
    isStaticAvailable: boolean;
    isDynamicAvailable: boolean;
    lastNetworkChange: Date;
    networkAdapter: string | null;
    subnetMask: string | null;
    gateway: string | null;
    dnsServers: string[];
}
export interface StaticIPConfig {
    targetIP: string;
    subnetMask: string;
    gateway: string;
    dnsServers: string[];
    adapterName: string;
}
export interface NetworkChangeEvent {
    type: 'static_failed' | 'static_restored' | 'dynamic_activated' | 'network_changed' | 'ip_conflict';
    oldConfig: NetworkConfiguration;
    newConfig: NetworkConfiguration;
    timestamp: Date;
    reason: string;
}
export declare class HybridNetworkService extends EventEmitter {
    private _config;
    private _monitoringInterval;
    private _isRunning;
    private _healthCheckInterval;
    private _networkChangeDetectionInterval;
    constructor(port?: number);
    /**
     * Start hybrid network management
     */
    start(): Promise<void>;
    /**
     * Stop hybrid network management
     */
    stop(): Promise<void>;
    /**
     * Analyze current network configuration
     */
    private analyzeCurrentNetwork;
    /**
     * Attempt to assign static IP
     */
    private attemptStaticIPAssignment;
    /**
     * Initialize network discovery as backup
     */
    private initializeNetworkDiscovery;
    /**
     * Start continuous network monitoring
     */
    private startNetworkMonitoring;
    /**
     * Perform network health check
     */
    private performNetworkHealthCheck;
    /**
     * Switch to dynamic mode
     */
    private switchToDynamicMode;
    /**
     * Test IP connectivity
     */
    private testIPConnectivity;
    /**
     * Emit network change event
     */
    private emitNetworkChange;
    /**
     * Get current network configuration
     */
    getNetworkConfiguration(): NetworkConfiguration;
    /**
     * Get current server URL
     */
    getServerURL(): string;
    /**
     * Check if service is running
     */
    isRunning(): boolean;
    /**
     * Force switch to static mode (if available)
     */
    switchToStaticMode(): Promise<boolean>;
    /**
     * Force switch to dynamic mode
     */
    switchToDynamicModeManual(): Promise<boolean>;
    private getCurrentNetworkInfo;
    private findOptimalStaticIP;
    private assignStaticIP;
}
export declare const hybridNetworkService: HybridNetworkService;
