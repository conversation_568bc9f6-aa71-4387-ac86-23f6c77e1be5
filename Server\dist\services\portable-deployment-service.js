"use strict";
/**
 * Portable Deployment Service
 * Handles dynamic path resolution and configuration generation for unknown deployment paths
 * Enables VMS to run from any location without hardcoded paths
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.portableDeploymentService = exports.PortableDeploymentService = void 0;
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const fs_1 = require("fs");
const os_1 = __importDefault(require("os"));
const logger_js_1 = require("../utils/logger.js");
class PortableDeploymentService {
    _deploymentConfig = null;
    _isInitialized = false;
    /**
     * Initialize portable deployment system
     */
    async initialize() {
        try {
            logger_js_1.logger.info('🔧 Initializing Portable Deployment Service...');
            // Detect deployment paths
            const paths = await this.detectDeploymentPaths();
            // Get system information
            const systemInfo = this.getSystemInfo();
            // Create deployment configuration
            this._deploymentConfig = {
                deploymentId: this.generateDeploymentId(),
                deploymentTime: new Date(),
                deploymentMode: this.detectDeploymentMode(),
                serverIP: await this.detectServerIP(),
                serverPort: parseInt(process.env.PORT || '8080'),
                paths,
                systemInfo
            };
            // Ensure all directories exist
            await this.ensureDirectoriesExist();
            // Generate configuration files
            await this.generateConfigurationFiles();
            // Update environment variables
            this.updateEnvironmentVariables();
            this._isInitialized = true;
            logger_js_1.logger.info('✅ Portable Deployment Service initialized successfully');
            logger_js_1.logger.info(`📁 VMS Root: ${this._deploymentConfig.paths.vmsRoot}`);
            logger_js_1.logger.info(`🖥️ Server IP: ${this._deploymentConfig.serverIP}:${this._deploymentConfig.serverPort}`);
            logger_js_1.logger.info(`🆔 Deployment ID: ${this._deploymentConfig.deploymentId}`);
            return this._deploymentConfig;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to initialize Portable Deployment Service:', error);
            throw error;
        }
    }
    /**
     * Detect deployment paths dynamically
     */
    async detectDeploymentPaths() {
        try {
            // Method 1: Use current working directory and traverse up
            let vmsRoot = await this.findVMSRoot(process.cwd());
            // Method 2: Use script location
            if (!vmsRoot) {
                const scriptDir = path_1.default.dirname(process.argv[1] || __dirname);
                vmsRoot = await this.findVMSRoot(scriptDir);
            }
            // Method 3: Use __dirname and traverse up
            if (!vmsRoot) {
                vmsRoot = await this.findVMSRoot(__dirname);
            }
            // Method 4: Search common locations
            if (!vmsRoot) {
                vmsRoot = await this.searchCommonLocations();
            }
            if (!vmsRoot) {
                throw new Error('VMS installation directory not found');
            }
            // Resolve all paths relative to VMS root
            const paths = {
                vmsRoot: path_1.default.resolve(vmsRoot),
                serverRoot: path_1.default.resolve(vmsRoot, 'Server'),
                clientRoot: path_1.default.resolve(vmsRoot, 'Client'),
                toolsRoot: path_1.default.resolve(vmsRoot, 'Tools'),
                logsRoot: path_1.default.resolve(vmsRoot, 'Logs'),
                configRoot: path_1.default.resolve(vmsRoot, 'Config'),
                databasePath: path_1.default.resolve(vmsRoot, 'Database'),
                backupRoot: path_1.default.resolve(vmsRoot, 'Backups')
            };
            // Validate critical paths
            await this.validatePaths(paths);
            return paths;
        }
        catch (error) {
            logger_js_1.logger.error('Error detecting deployment paths:', error);
            throw error;
        }
    }
    /**
     * Find VMS root directory by traversing up from a starting directory
     */
    async findVMSRoot(startDir) {
        try {
            let currentDir = path_1.default.resolve(startDir);
            const maxLevels = 10; // Prevent infinite loops
            for (let i = 0; i < maxLevels; i++) {
                if (await this.isVMSRoot(currentDir)) {
                    return currentDir;
                }
                const parentDir = path_1.default.dirname(currentDir);
                if (parentDir === currentDir) {
                    // Reached filesystem root
                    break;
                }
                currentDir = parentDir;
            }
            return null;
        }
        catch (error) {
            logger_js_1.logger.debug(`Error finding VMS root from ${startDir}:`, error);
            return null;
        }
    }
    /**
     * Check if directory is VMS root
     */
    async isVMSRoot(dir) {
        try {
            const requiredPaths = [
                path_1.default.join(dir, 'Server'),
                path_1.default.join(dir, 'Server', 'dist', 'index.js'),
                path_1.default.join(dir, 'Server', 'package.json'),
                path_1.default.join(dir, 'Client')
            ];
            for (const requiredPath of requiredPaths) {
                if (!(0, fs_1.existsSync)(requiredPath)) {
                    return false;
                }
            }
            // Additional validation: check package.json for VMS markers
            const packageJsonPath = path_1.default.join(dir, 'Server', 'package.json');
            try {
                const packageJson = JSON.parse(await promises_1.default.readFile(packageJsonPath, 'utf-8'));
                return packageJson.name && packageJson.name.toLowerCase().includes('vms');
            }
            catch {
                return true; // If we can't read package.json, but other files exist, assume it's VMS
            }
        }
        catch {
            return false;
        }
    }
    /**
     * Search common VMS installation locations
     */
    async searchCommonLocations() {
        const commonPaths = [
            'C:\\VMS-PRODUCTION',
            'C:\\Program Files\\VMS-PRODUCTION',
            'C:\\Program Files (x86)\\VMS-PRODUCTION',
            path_1.default.join(os_1.default.homedir(), 'Desktop', 'VMS-PRODUCTION'),
            path_1.default.join(os_1.default.homedir(), 'Documents', 'VMS-PRODUCTION'),
            path_1.default.join(os_1.default.homedir(), 'VMS-PRODUCTION'),
            '/opt/VMS-PRODUCTION',
            '/usr/local/VMS-PRODUCTION',
            path_1.default.join(os_1.default.homedir(), 'VMS-PRODUCTION')
        ];
        for (const searchPath of commonPaths) {
            try {
                if (await this.isVMSRoot(searchPath)) {
                    return searchPath;
                }
            }
            catch {
                // Continue searching
            }
        }
        // Search all drives on Windows
        if (os_1.default.platform() === 'win32') {
            try {
                const drives = await this.getWindowsDrives();
                for (const drive of drives) {
                    const vmsPath = path_1.default.join(drive, 'VMS-PRODUCTION');
                    if (await this.isVMSRoot(vmsPath)) {
                        return vmsPath;
                    }
                }
            }
            catch {
                // Continue
            }
        }
        return null;
    }
    /**
     * Get available Windows drives
     */
    async getWindowsDrives() {
        const drives = [];
        for (let i = 65; i <= 90; i++) { // A-Z
            const drive = String.fromCharCode(i) + ':';
            try {
                await promises_1.default.access(drive);
                drives.push(drive);
            }
            catch {
                // Drive not available
            }
        }
        return drives;
    }
    /**
     * Validate that critical paths exist
     */
    async validatePaths(paths) {
        const criticalPaths = [
            { path: paths.vmsRoot, name: 'VMS Root' },
            { path: paths.serverRoot, name: 'Server Root' },
            { path: path_1.default.join(paths.serverRoot, 'dist', 'index.js'), name: 'Server Entry Point' }
        ];
        for (const { path: checkPath, name } of criticalPaths) {
            if (!(0, fs_1.existsSync)(checkPath)) {
                throw new Error(`Critical path missing: ${name} (${checkPath})`);
            }
        }
        logger_js_1.logger.info('✅ All critical paths validated');
    }
    /**
     * Ensure all required directories exist
     */
    async ensureDirectoriesExist() {
        if (!this._deploymentConfig) {
            throw new Error('Deployment config not initialized');
        }
        const directories = [
            this._deploymentConfig.paths.logsRoot,
            this._deploymentConfig.paths.configRoot,
            this._deploymentConfig.paths.backupRoot,
            path_1.default.join(this._deploymentConfig.paths.configRoot, 'generated'),
            path_1.default.join(this._deploymentConfig.paths.logsRoot, 'server'),
            path_1.default.join(this._deploymentConfig.paths.logsRoot, 'deployment'),
            path_1.default.join(this._deploymentConfig.paths.backupRoot, 'automated'),
            path_1.default.join(this._deploymentConfig.paths.backupRoot, 'manual')
        ];
        for (const dir of directories) {
            try {
                await promises_1.default.mkdir(dir, { recursive: true });
                logger_js_1.logger.debug(`📁 Ensured directory exists: ${dir}`);
            }
            catch (error) {
                logger_js_1.logger.warn(`⚠️ Could not create directory ${dir}:`, error);
            }
        }
        logger_js_1.logger.info('✅ All required directories ensured');
    }
    /**
     * Generate configuration files with dynamic paths
     */
    async generateConfigurationFiles() {
        if (!this._deploymentConfig) {
            throw new Error('Deployment config not initialized');
        }
        const configDir = path_1.default.join(this._deploymentConfig.paths.configRoot, 'generated');
        // Generate paths configuration
        const pathsConfig = {
            generated: new Date().toISOString(),
            deploymentId: this._deploymentConfig.deploymentId,
            paths: this._deploymentConfig.paths,
            relativePaths: this.generateRelativePaths()
        };
        await promises_1.default.writeFile(path_1.default.join(configDir, 'paths.json'), JSON.stringify(pathsConfig, null, 2));
        // Generate environment configuration
        const envConfig = {
            NODE_ENV: process.env.NODE_ENV || 'production',
            PORT: this._deploymentConfig.serverPort.toString(),
            HOST: '0.0.0.0',
            VMS_ROOT: this._deploymentConfig.paths.vmsRoot,
            VMS_SERVER_ROOT: this._deploymentConfig.paths.serverRoot,
            VMS_LOGS_ROOT: this._deploymentConfig.paths.logsRoot,
            VMS_CONFIG_ROOT: this._deploymentConfig.paths.configRoot,
            VMS_BACKUP_ROOT: this._deploymentConfig.paths.backupRoot,
            VMS_DEPLOYMENT_ID: this._deploymentConfig.deploymentId,
            VMS_DEPLOYMENT_MODE: this._deploymentConfig.deploymentMode,
            VMS_SERVER_IP: this._deploymentConfig.serverIP,
            VMS_PORTABLE_MODE: 'true'
        };
        // Write .env file
        const envContent = Object.entries(envConfig)
            .map(([key, value]) => `${key}=${value}`)
            .join('\n');
        await promises_1.default.writeFile(path_1.default.join(this._deploymentConfig.paths.serverRoot, '.env'), envContent);
        // Generate deployment summary
        await promises_1.default.writeFile(path_1.default.join(configDir, 'deployment-config.json'), JSON.stringify(this._deploymentConfig, null, 2));
        logger_js_1.logger.info('✅ Configuration files generated');
    }
    /**
     * Generate relative paths for portability
     */
    generateRelativePaths() {
        if (!this._deploymentConfig) {
            return {};
        }
        const vmsRoot = this._deploymentConfig.paths.vmsRoot;
        return {
            server: path_1.default.relative(vmsRoot, this._deploymentConfig.paths.serverRoot),
            client: path_1.default.relative(vmsRoot, this._deploymentConfig.paths.clientRoot),
            tools: path_1.default.relative(vmsRoot, this._deploymentConfig.paths.toolsRoot),
            logs: path_1.default.relative(vmsRoot, this._deploymentConfig.paths.logsRoot),
            config: path_1.default.relative(vmsRoot, this._deploymentConfig.paths.configRoot),
            backups: path_1.default.relative(vmsRoot, this._deploymentConfig.paths.backupRoot)
        };
    }
    /**
     * Update environment variables for current process
     */
    updateEnvironmentVariables() {
        if (!this._deploymentConfig) {
            return;
        }
        const envVars = {
            VMS_ROOT: this._deploymentConfig.paths.vmsRoot,
            VMS_SERVER_ROOT: this._deploymentConfig.paths.serverRoot,
            VMS_LOGS_ROOT: this._deploymentConfig.paths.logsRoot,
            VMS_CONFIG_ROOT: this._deploymentConfig.paths.configRoot,
            VMS_BACKUP_ROOT: this._deploymentConfig.paths.backupRoot,
            VMS_DEPLOYMENT_ID: this._deploymentConfig.deploymentId,
            VMS_DEPLOYMENT_MODE: this._deploymentConfig.deploymentMode,
            VMS_SERVER_IP: this._deploymentConfig.serverIP,
            VMS_PORTABLE_MODE: 'true'
        };
        Object.entries(envVars).forEach(([key, value]) => {
            process.env[key] = value;
        });
        logger_js_1.logger.info('✅ Environment variables updated');
    }
    /**
     * Detect deployment mode
     */
    detectDeploymentMode() {
        if (process.env.VMS_SERVICE_MODE === 'true') {
            return 'service';
        }
        if (process.env.VMS_DEPLOYMENT_MODE === 'automated') {
            return 'automated';
        }
        return 'manual';
    }
    /**
     * Detect server IP address
     */
    async detectServerIP() {
        try {
            const interfaces = os_1.default.networkInterfaces();
            // Prefer Ethernet, then WiFi
            const preferredTypes = ['Ethernet', 'Wi-Fi', 'WiFi', 'Wireless'];
            for (const type of preferredTypes) {
                for (const [name, addresses] of Object.entries(interfaces)) {
                    if (name.toLowerCase().includes(type.toLowerCase()) && addresses) {
                        for (const addr of addresses) {
                            if (addr.family === 'IPv4' && !addr.internal && addr.address !== '127.0.0.1') {
                                return addr.address;
                            }
                        }
                    }
                }
            }
            // Fallback: any non-internal IPv4 address
            for (const addresses of Object.values(interfaces)) {
                if (addresses) {
                    for (const addr of addresses) {
                        if (addr.family === 'IPv4' && !addr.internal && addr.address !== '127.0.0.1') {
                            return addr.address;
                        }
                    }
                }
            }
            return 'localhost';
        }
        catch (error) {
            logger_js_1.logger.warn('Error detecting server IP:', error);
            return 'localhost';
        }
    }
    /**
     * Get system information
     */
    getSystemInfo() {
        return {
            platform: os_1.default.platform(),
            hostname: os_1.default.hostname(),
            username: os_1.default.userInfo().username,
            nodeVersion: process.version
        };
    }
    /**
     * Generate unique deployment ID
     */
    generateDeploymentId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 8);
        const hostname = os_1.default.hostname().substring(0, 8).toLowerCase();
        return `vms-${hostname}-${timestamp}-${random}`;
    }
    /**
     * Get deployment configuration
     */
    getDeploymentConfig() {
        return this._deploymentConfig;
    }
    /**
     * Get deployment paths
     */
    getDeploymentPaths() {
        return this._deploymentConfig?.paths || null;
    }
    /**
     * Check if service is initialized
     */
    isInitialized() {
        return this._isInitialized;
    }
    /**
     * Resolve path relative to VMS root
     */
    resolvePath(...pathSegments) {
        if (!this._deploymentConfig) {
            throw new Error('Portable deployment service not initialized');
        }
        return path_1.default.resolve(this._deploymentConfig.paths.vmsRoot, ...pathSegments);
    }
    /**
     * Get absolute path for a relative path
     */
    getAbsolutePath(relativePath) {
        if (!this._deploymentConfig) {
            throw new Error('Portable deployment service not initialized');
        }
        if (path_1.default.isAbsolute(relativePath)) {
            return relativePath;
        }
        return path_1.default.resolve(this._deploymentConfig.paths.vmsRoot, relativePath);
    }
}
exports.PortableDeploymentService = PortableDeploymentService;
// Export singleton instance
exports.portableDeploymentService = new PortableDeploymentService();
//# sourceMappingURL=portable-deployment-service.js.map