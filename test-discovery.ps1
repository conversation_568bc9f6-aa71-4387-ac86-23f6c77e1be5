# Test VMS Server Discovery
Write-Host "🔍 Testing VMS Server Discovery..." -ForegroundColor Cyan

try {
    # Test 1: Check if ports are listening
    Write-Host "`n1. Checking if discovery ports are listening..." -ForegroundColor Yellow
    
    $port45454 = Get-NetTCPConnection -LocalPort 45454 -ErrorAction SilentlyContinue
    $port45455 = Get-NetTCPConnection -LocalPort 45455 -ErrorAction SilentlyContinue
    
    if ($port45454) {
        Write-Host "✅ Port 45454 is in use" -ForegroundColor Green
    } else {
        Write-Host "❌ Port 45454 is not listening" -ForegroundColor Red
    }
    
    if ($port45455) {
        Write-Host "✅ Port 45455 is in use" -ForegroundColor Green
    } else {
        Write-Host "❌ Port 45455 is not listening" -ForegroundColor Red
    }
    
    # Test 2: Check UDP ports specifically
    Write-Host "`n2. Checking UDP ports..." -ForegroundColor Yellow
    $udpPorts = Get-NetUDPEndpoint | Where-Object { $_.LocalPort -eq 45454 -or $_.LocalPort -eq 45455 }
    
    if ($udpPorts) {
        Write-Host "✅ Found UDP endpoints:" -ForegroundColor Green
        $udpPorts | ForEach-Object { Write-Host "   Port $($_.LocalPort) on $($_.LocalAddress)" -ForegroundColor Green }
    } else {
        Write-Host "❌ No UDP endpoints found on ports 45454/45455" -ForegroundColor Red
    }
    
    # Test 3: Try to send discovery request
    Write-Host "`n3. Sending discovery request..." -ForegroundColor Yellow
    
    $udpClient = New-Object System.Net.Sockets.UdpClient
    $udpClient.EnableBroadcast = $true
    
    $request = @{
        type = "VMS_DISCOVERY_REQUEST"
        clientId = $env:COMPUTERNAME
        timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
    } | ConvertTo-Json
    
    $requestBytes = [System.Text.Encoding]::UTF8.GetBytes($request)
    
    # Send to localhost
    $endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Parse("127.0.0.1"), 45455)
    $udpClient.Send($requestBytes, $requestBytes.Length, $endpoint)
    Write-Host "📤 Discovery request sent to 127.0.0.1:45455" -ForegroundColor Green
    
    # Send to broadcast
    $broadcastEndpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Parse("***************"), 45455)
    $udpClient.Send($requestBytes, $requestBytes.Length, $broadcastEndpoint)
    Write-Host "📤 Discovery request sent to ***************:45455" -ForegroundColor Green
    
    $udpClient.Close()
    
    Write-Host "`n4. Check server logs for discovery responses..." -ForegroundColor Yellow
    Write-Host "✅ Discovery test completed" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error during discovery test: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nPress any key to continue..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey('NoEcho,IncludeKeyDown')
