"use strict";
/**
 * VMS-ADMIN Service
 * Web-based administration dashboard service
 * Runs on port 8081 alongside VMS server (port 8080)
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.vmsAdminService = exports.VMSAdminService = void 0;
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const path_1 = __importDefault(require("path"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const express_session_1 = __importDefault(require("express-session"));
const network_integration_service_js_1 = require("./network-integration-service.js");
const hybrid_network_service_js_1 = require("./hybrid-network-service.js");
const intelligent_fallback_service_js_1 = require("./intelligent-fallback-service.js");
const self_healing_network_monitor_js_1 = require("./self-healing-network-monitor.js");
const logger_js_1 = require("../utils/logger.js");
const database_js_1 = require("../database/database.js");
// Use process.cwd() instead of import.meta for CommonJS compatibility
const __dirname = process.cwd();
class VMSAdminService {
    _app;
    _server;
    _io;
    _isRunning = false;
    _port = 8081;
    _connectedAdmins = new Map();
    constructor() {
        this._app = (0, express_1.default)();
        this._server = (0, http_1.createServer)(this._app);
        this._io = new socket_io_1.Server(this._server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });
        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketHandlers();
        logger_js_1.logger.info('🎛️ VMS-ADMIN Service initialized');
    }
    /**
     * Setup Express middleware
     */
    setupMiddleware() {
        // Session management
        this._app.use((0, express_session_1.default)({
            name: 'vms-admin-session',
            secret: process.env.ADMIN_SESSION_SECRET || 'vms-admin-secret-key-change-in-production',
            resave: false,
            saveUninitialized: false,
            cookie: {
                secure: false, // Set to true for HTTPS
                httpOnly: true,
                maxAge: 24 * 60 * 60 * 1000 // 24 hours
            }
        }));
        // Body parsing
        this._app.use(express_1.default.json());
        this._app.use(express_1.default.urlencoded({ extended: true }));
        // Static files for dashboard UI
        const dashboardPath = path_1.default.join(__dirname, '../../dashboard');
        this._app.use(express_1.default.static(dashboardPath));
        // CORS for API endpoints
        this._app.use('/api', (req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
            if (req.method === 'OPTIONS') {
                res.sendStatus(200);
            }
            else {
                next();
            }
        });
        logger_js_1.logger.info('🔧 VMS-ADMIN middleware configured');
    }
    /**
     * Setup Express routes
     */
    setupRoutes() {
        // Health check endpoint
        this._app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                service: 'VMS-ADMIN',
                version: '1.0.0',
                timestamp: new Date(),
                uptime: process.uptime()
            });
        });
        // Authentication routes
        this._app.post('/api/auth/login', this.handleLogin.bind(this));
        this._app.post('/api/auth/logout', this.handleLogout.bind(this));
        this._app.get('/api/auth/status', this.handleAuthStatus.bind(this));
        // Protected API routes (require admin authentication)
        this._app.use('/api', this.requireAdminAuth.bind(this));
        // Dashboard data endpoints
        this._app.get('/api/dashboard/stats', this.getDashboardStats.bind(this));
        this._app.get('/api/dashboard/system-status', this.getSystemStatus.bind(this));
        this._app.get('/api/dashboard/network-status', this.getNetworkStatus.bind(this));
        // System control endpoints
        this._app.post('/api/system/restart-vms', this.restartVMSServer.bind(this));
        this._app.post('/api/system/switch-network-mode', this.switchNetworkMode.bind(this));
        this._app.post('/api/system/trigger-healing', this.triggerHealing.bind(this));
        // Database management endpoints
        this._app.get('/api/database/status', this.getDatabaseStatus.bind(this));
        this._app.post('/api/database/backup', this.createDatabaseBackup.bind(this));
        this._app.get('/api/database/backups', this.listDatabaseBackups.bind(this));
        // User management endpoints
        this._app.get('/api/users/active', this.getActiveUsers.bind(this));
        this._app.get('/api/users/sessions', this.getUserSessions.bind(this));
        // Logs endpoints
        this._app.get('/api/logs/recent', this.getRecentLogs.bind(this));
        this._app.get('/api/logs/download', this.downloadLogs.bind(this));
        // Default route - serve dashboard HTML
        this._app.get('/', (req, res) => {
            if (req.session && req.session.adminUser) {
                res.sendFile(path_1.default.join(__dirname, '../../dashboard/index.html'));
            }
            else {
                res.sendFile(path_1.default.join(__dirname, '../../dashboard/login.html'));
            }
        });
        logger_js_1.logger.info('🛣️ VMS-ADMIN routes configured');
    }
    /**
     * Setup Socket.IO handlers for real-time updates
     */
    setupSocketHandlers() {
        this._io.on('connection', (socket) => {
            logger_js_1.logger.info(`🔌 Admin client connected: ${socket.id}`);
            // Authenticate socket connection
            socket.on('authenticate', async (data) => {
                try {
                    const { sessionId } = data;
                    // Verify session and get admin user
                    const adminUser = await this.getAdminUserBySession(sessionId);
                    if (adminUser) {
                        socket.data.adminUser = adminUser;
                        socket.join('admin-room');
                        socket.emit('authenticated', { success: true, user: adminUser });
                        // Send initial dashboard data
                        const stats = await this.collectDashboardStats();
                        socket.emit('dashboard-stats', stats);
                        logger_js_1.logger.info(`✅ Admin authenticated via socket: ${adminUser.username}`);
                    }
                    else {
                        socket.emit('authenticated', { success: false, error: 'Invalid session' });
                    }
                }
                catch (error) {
                    socket.emit('authenticated', { success: false, error: 'Authentication failed' });
                }
            });
            // Handle real-time requests
            socket.on('request-stats', async () => {
                if (socket.data.adminUser) {
                    const stats = await this.collectDashboardStats();
                    socket.emit('dashboard-stats', stats);
                }
            });
            socket.on('disconnect', () => {
                logger_js_1.logger.info(`🔌 Admin client disconnected: ${socket.id}`);
            });
        });
        // Setup real-time event broadcasting
        this.setupRealTimeUpdates();
        logger_js_1.logger.info('🔌 VMS-ADMIN Socket.IO configured');
    }
    /**
     * Setup real-time updates from network services
     */
    setupRealTimeUpdates() {
        // Listen to network integration events
        network_integration_service_js_1.networkIntegrationService.on('networkChange', (event) => {
            this._io.to('admin-room').emit('network-change', event);
        });
        network_integration_service_js_1.networkIntegrationService.on('fallbackDecision', (decision) => {
            this._io.to('admin-room').emit('fallback-decision', decision);
        });
        network_integration_service_js_1.networkIntegrationService.on('healingAction', (action) => {
            this._io.to('admin-room').emit('healing-action', action);
        });
        // Periodic stats updates
        setInterval(async () => {
            try {
                const stats = await this.collectDashboardStats();
                this._io.to('admin-room').emit('dashboard-stats', stats);
            }
            catch (error) {
                logger_js_1.logger.error('Error broadcasting stats update:', error);
            }
        }, 30000); // Every 30 seconds
        logger_js_1.logger.info('📡 Real-time updates configured');
    }
    /**
     * Handle admin login
     */
    async handleLogin(req, res) {
        try {
            const { username, password } = req.body;
            if (!username || !password) {
                res.status(400).json({ success: false, error: 'Username and password required' });
                return;
            }
            // Query VMS database for admin user
            const user = await database_js_1.db.get(`
        SELECT id, username, password_hash, role, department, permissions, status 
        FROM users 
        WHERE username = ? AND role IN ('admin', 'super_admin') AND status = 'active'
      `, [username]);
            if (!user) {
                res.status(401).json({ success: false, error: 'Invalid credentials or insufficient permissions' });
                return;
            }
            // Verify password
            const isValidPassword = await bcrypt_1.default.compare(password, user.password_hash);
            if (!isValidPassword) {
                res.status(401).json({ success: false, error: 'Invalid credentials' });
                return;
            }
            // Create admin session
            const adminUser = {
                id: user.id,
                username: user.username,
                role: user.role,
                department: user.department,
                permissions: user.permissions ? JSON.parse(user.permissions) : [],
                lastLogin: new Date()
            };
            req.session.adminUser = adminUser;
            this._connectedAdmins.set(req.sessionID, adminUser);
            // Update last login in database
            await database_js_1.db.run('UPDATE users SET last_login = ? WHERE id = ?', [new Date(), user.id]);
            logger_js_1.logger.info(`✅ Admin login successful: ${username} (${user.role})`);
            res.json({
                success: true,
                user: {
                    id: adminUser.id,
                    username: adminUser.username,
                    role: adminUser.role,
                    department: adminUser.department,
                    permissions: adminUser.permissions
                }
            });
        }
        catch (error) {
            logger_js_1.logger.error('Admin login error:', error);
            res.status(500).json({ success: false, error: 'Login failed' });
        }
    }
    /**
     * Handle admin logout
     */
    async handleLogout(req, res) {
        try {
            const sessionId = req.sessionID;
            if (this._connectedAdmins.has(sessionId)) {
                const adminUser = this._connectedAdmins.get(sessionId);
                this._connectedAdmins.delete(sessionId);
                logger_js_1.logger.info(`👋 Admin logout: ${adminUser?.username}`);
            }
            req.session.destroy((err) => {
                if (err) {
                    logger_js_1.logger.error('Session destruction error:', err);
                }
            });
            res.json({ success: true });
        }
        catch (error) {
            logger_js_1.logger.error('Admin logout error:', error);
            res.status(500).json({ success: false, error: 'Logout failed' });
        }
    }
    /**
     * Handle authentication status check
     */
    async handleAuthStatus(req, res) {
        try {
            const adminUser = req.session?.adminUser;
            if (adminUser) {
                res.json({
                    authenticated: true,
                    user: {
                        id: adminUser.id,
                        username: adminUser.username,
                        role: adminUser.role,
                        department: adminUser.department,
                        permissions: adminUser.permissions
                    }
                });
            }
            else {
                res.json({ authenticated: false });
            }
        }
        catch (error) {
            logger_js_1.logger.error('Auth status check error:', error);
            res.status(500).json({ authenticated: false, error: 'Status check failed' });
        }
    }
    /**
     * Require admin authentication middleware
     */
    requireAdminAuth(req, res, next) {
        const adminUser = req.session?.adminUser;
        if (adminUser && (adminUser.role === 'admin' || adminUser.role === 'super_admin')) {
            next();
        }
        else {
            res.status(401).json({ success: false, error: 'Admin authentication required' });
        }
    }
    /**
     * Get dashboard statistics
     */
    async getDashboardStats(req, res) {
        try {
            const stats = await this.collectDashboardStats();
            res.json(stats);
        }
        catch (error) {
            logger_js_1.logger.error('Error getting dashboard stats:', error);
            res.status(500).json({ error: 'Failed to get dashboard stats' });
        }
    }
    /**
     * Collect comprehensive dashboard statistics
     */
    async collectDashboardStats() {
        try {
            // Get system status from network integration service
            const systemStatus = await network_integration_service_js_1.networkIntegrationService.getSystemStatus();
            const networkConfig = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
            // Get database status
            const dbStatus = await this.getDatabaseStatusInfo();
            // Get user statistics
            const userStats = await this.getUserStatistics();
            return {
                systemStatus: systemStatus.overall,
                vmsServer: {
                    status: systemStatus.components.hybridNetwork ? 'running' : 'stopped',
                    uptime: process.uptime() * 1000,
                    port: 8080,
                    connections: this._connectedAdmins.size
                },
                networkSystem: {
                    mode: networkConfig.mode,
                    staticIP: networkConfig.staticIP,
                    dynamicIP: networkConfig.dynamicIP,
                    connectivity: networkConfig.isStaticAvailable || networkConfig.isDynamicAvailable,
                    lastChange: networkConfig.lastNetworkChange
                },
                database: dbStatus,
                users: userStats
            };
        }
        catch (error) {
            logger_js_1.logger.error('Error collecting dashboard stats:', error);
            throw error;
        }
    }
    /**
     * Get database status information
     */
    async getDatabaseStatusInfo() {
        try {
            // Test database connection
            await database_js_1.db.get('SELECT 1');
            // Get database size (simplified)
            const tables = await database_js_1.db.all("SELECT name FROM sqlite_master WHERE type='table'");
            return {
                status: 'connected',
                size: tables.length,
                lastBackup: null // TODO: Implement backup tracking
            };
        }
        catch (error) {
            return {
                status: 'error',
                size: 0,
                lastBackup: null
            };
        }
    }
    /**
     * Get user statistics
     */
    async getUserStatistics() {
        try {
            const activeUsers = await database_js_1.db.all(`
        SELECT department, COUNT(*) as count 
        FROM users 
        WHERE status = 'active' 
        GROUP BY department
      `);
            const departmentBreakdown = {};
            let totalActive = 0;
            activeUsers.forEach((row) => {
                departmentBreakdown[row.department] = row.count;
                totalActive += row.count;
            });
            return {
                totalActive,
                adminSessions: this._connectedAdmins.size,
                departmentBreakdown
            };
        }
        catch (error) {
            logger_js_1.logger.error('Error getting user statistics:', error);
            return {
                totalActive: 0,
                adminSessions: 0,
                departmentBreakdown: {}
            };
        }
    }
    /**
     * Get system status
     */
    async getSystemStatus(req, res) {
        try {
            const status = await network_integration_service_js_1.networkIntegrationService.getSystemStatus();
            res.json(status);
        }
        catch (error) {
            logger_js_1.logger.error('Error getting system status:', error);
            res.status(500).json({ error: 'Failed to get system status' });
        }
    }
    /**
     * Get network status
     */
    async getNetworkStatus(req, res) {
        try {
            const networkConfig = hybrid_network_service_js_1.hybridNetworkService.getNetworkConfiguration();
            const fallbackStats = intelligent_fallback_service_js_1.intelligentFallbackService.getStats();
            const healingStats = self_healing_network_monitor_js_1.selfHealingNetworkMonitor.getStats();
            res.json({
                configuration: networkConfig,
                fallbackStats,
                healingStats
            });
        }
        catch (error) {
            logger_js_1.logger.error('Error getting network status:', error);
            res.status(500).json({ error: 'Failed to get network status' });
        }
    }
    /**
     * Restart VMS server
     */
    async restartVMSServer(req, res) {
        try {
            const success = await network_integration_service_js_1.networkIntegrationService.restartServices();
            res.json({ success, message: success ? 'VMS server restarted successfully' : 'Failed to restart VMS server' });
        }
        catch (error) {
            logger_js_1.logger.error('Error restarting VMS server:', error);
            res.status(500).json({ success: false, error: 'Failed to restart VMS server' });
        }
    }
    /**
     * Switch network mode
     */
    async switchNetworkMode(req, res) {
        try {
            const { mode } = req.body;
            if (!mode || !['static', 'dynamic'].includes(mode)) {
                res.status(400).json({ success: false, error: 'Invalid network mode' });
                return;
            }
            const success = await network_integration_service_js_1.networkIntegrationService.switchNetworkMode(mode);
            res.json({
                success,
                message: success ? `Switched to ${mode} mode successfully` : `Failed to switch to ${mode} mode`
            });
        }
        catch (error) {
            logger_js_1.logger.error('Error switching network mode:', error);
            res.status(500).json({ success: false, error: 'Failed to switch network mode' });
        }
    }
    /**
     * Trigger healing
     */
    async triggerHealing(req, res) {
        try {
            const success = await network_integration_service_js_1.networkIntegrationService.triggerHealing();
            res.json({
                success,
                message: success ? 'Network healing triggered successfully' : 'Failed to trigger network healing'
            });
        }
        catch (error) {
            logger_js_1.logger.error('Error triggering healing:', error);
            res.status(500).json({ success: false, error: 'Failed to trigger healing' });
        }
    }
    /**
     * Get database status
     */
    async getDatabaseStatus(req, res) {
        try {
            const status = await this.getDatabaseStatusInfo();
            res.json(status);
        }
        catch (error) {
            logger_js_1.logger.error('Error getting database status:', error);
            res.status(500).json({ error: 'Failed to get database status' });
        }
    }
    /**
     * Create database backup
     */
    async createDatabaseBackup(req, res) {
        try {
            // TODO: Implement database backup functionality
            res.json({ success: false, message: 'Database backup functionality not yet implemented' });
        }
        catch (error) {
            logger_js_1.logger.error('Error creating database backup:', error);
            res.status(500).json({ success: false, error: 'Failed to create database backup' });
        }
    }
    /**
     * List database backups
     */
    async listDatabaseBackups(req, res) {
        try {
            // TODO: Implement backup listing functionality
            res.json([]);
        }
        catch (error) {
            logger_js_1.logger.error('Error listing database backups:', error);
            res.status(500).json({ error: 'Failed to list database backups' });
        }
    }
    /**
     * Get active users
     */
    async getActiveUsers(req, res) {
        try {
            const users = await database_js_1.db.all(`
        SELECT id, username, role, department, last_login, status
        FROM users
        WHERE status = 'active'
        ORDER BY last_login DESC
      `);
            res.json(users);
        }
        catch (error) {
            logger_js_1.logger.error('Error getting active users:', error);
            res.status(500).json({ error: 'Failed to get active users' });
        }
    }
    /**
     * Get user sessions
     */
    async getUserSessions(req, res) {
        try {
            const sessions = Array.from(this._connectedAdmins.values()).map(user => ({
                username: user.username,
                role: user.role,
                department: user.department,
                lastLogin: user.lastLogin
            }));
            res.json(sessions);
        }
        catch (error) {
            logger_js_1.logger.error('Error getting user sessions:', error);
            res.status(500).json({ error: 'Failed to get user sessions' });
        }
    }
    /**
     * Get recent logs
     */
    async getRecentLogs(req, res) {
        try {
            // TODO: Implement log reading functionality
            res.json([]);
        }
        catch (error) {
            logger_js_1.logger.error('Error getting recent logs:', error);
            res.status(500).json({ error: 'Failed to get recent logs' });
        }
    }
    /**
     * Download logs
     */
    async downloadLogs(req, res) {
        try {
            // TODO: Implement log download functionality
            res.status(501).json({ error: 'Log download not yet implemented' });
        }
        catch (error) {
            logger_js_1.logger.error('Error downloading logs:', error);
            res.status(500).json({ error: 'Failed to download logs' });
        }
    }
    /**
     * Start VMS-ADMIN service
     */
    async start() {
        try {
            if (this._isRunning) {
                logger_js_1.logger.warn('VMS-ADMIN Service is already running');
                return;
            }
            return new Promise((resolve, reject) => {
                this._server.listen(this._port, () => {
                    this._isRunning = true;
                    logger_js_1.logger.info(`🎛️ VMS-ADMIN Service started on port ${this._port}`);
                    logger_js_1.logger.info(`🌐 Admin dashboard available at: http://localhost:${this._port}`);
                    resolve();
                });
                this._server.on('error', (error) => {
                    logger_js_1.logger.error(`❌ VMS-ADMIN Service failed to start: ${error.message}`);
                    reject(error);
                });
            });
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to start VMS-ADMIN Service:', error);
            throw error;
        }
    }
    /**
     * Stop VMS-ADMIN service
     */
    async stop() {
        try {
            if (!this._isRunning) {
                return;
            }
            return new Promise((resolve) => {
                this._server.close(() => {
                    this._isRunning = false;
                    this._connectedAdmins.clear();
                    logger_js_1.logger.info('🛑 VMS-ADMIN Service stopped');
                    resolve();
                });
            });
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error stopping VMS-ADMIN Service:', error);
        }
    }
    /**
     * Get admin user by session ID
     */
    async getAdminUserBySession(sessionId) {
        return this._connectedAdmins.get(sessionId) || null;
    }
    /**
     * Check if service is running
     */
    isRunning() {
        return this._isRunning;
    }
    /**
     * Get service port
     */
    getPort() {
        return this._port;
    }
}
exports.VMSAdminService = VMSAdminService;
// Export singleton instance
exports.vmsAdminService = new VMSAdminService();
//# sourceMappingURL=vms-admin-service.js.map