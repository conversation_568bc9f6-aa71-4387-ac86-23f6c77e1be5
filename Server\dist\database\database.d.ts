/**
 * Database Module
 * Simple database interface for VMS-ADMIN service
 */
export interface DatabaseInterface {
    get(sql: string, params?: any[]): Promise<any>;
    all(sql: string, params?: any[]): Promise<any[]>;
    run(sql: string, params?: any[]): Promise<any>;
}
declare class Database implements DatabaseInterface {
    private db;
    constructor();
    get(sql: string, params?: any[]): Promise<any>;
    all(sql: string, params?: any[]): Promise<any[]>;
    run(sql: string, params?: any[]): Promise<any>;
}
export declare const db: Database;
export {};
