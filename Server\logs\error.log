{"level":"error","message":"listen EADDRINUSE: address already in use 0.0.0.0:8080","pid":22004,"service":"vms-server","stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:8080\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at node:net:2203:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-08-02T22:53:21.279Z"}
{"level":"error","message":"💥 FATAL: Recovery failed, exiting...","service":"vms-server","timestamp":"2025-08-02 22:53:26"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-02 22:54:36"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch 3d26e6d9-9b6a-47c0-b67d-ead4d6ede804","service":"vms-server","timestamp":"2025-08-02 22:56:01"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-02 22:56:48"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch fe21ffb8-84f3-4a33-9e91-38d4495012d0","service":"vms-server","timestamp":"2025-08-02 22:58:01"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-02 23:03:52"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 16:00:26"}
{"level":"error","message":"🔍 VOUCHER UPDATE DEBUG: No valid updates for voucher f622a8e4-0fc1-4719-b39c-e8bd162b840d. Received fields: isOnHold, is_on_hold, holdComment, hold_comment","service":"vms-server","timestamp":"2025-08-03 16:01:27"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 16:13:48"}
{"level":"error","message":"listen EADDRINUSE: address already in use 0.0.0.0:8080","pid":24924,"service":"vms-server","stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:8080\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at node:net:2203:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-08-03T16:55:51.670Z"}
{"level":"error","message":"💥 FATAL: Recovery failed, exiting...","service":"vms-server","timestamp":"2025-08-03 16:55:56"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch 97723850-e77c-4590-8032-2ee7e362ebf5","service":"vms-server","timestamp":"2025-08-03 17:15:11"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch 42246e9f-c99a-49d1-9449-14539c9fded0","service":"vms-server","timestamp":"2025-08-03 17:16:14"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch 131e4ab4-8d36-40a0-8b45-2e2cdd752c07","service":"vms-server","timestamp":"2025-08-03 17:18:24"}
{"level":"error","message":"❌ BATCH RECEIVE: Timeout after 15 seconds for batch b8fb763c-0030-4be0-8dd4-29569b729104","service":"vms-server","timestamp":"2025-08-03 17:22:28"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 17:23:33"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 17:25:52"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 17:52:07"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 18:01:43"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-03 18:01:49"}
{"cmd":"powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq \"Up\"} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne \"WellKnown\"} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \"\" }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse(\"***************\").Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { \"\" }             Gateway = if($gateway) { $gateway.NextHop } else { \"\" }             DnsServers = if($dns) { $dns.ServerAddresses -join \",\" } else { \"\" }             DhcpEnabled = $ipAddress.PrefixOrigin -eq \"Dhcp\"             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"","code":1,"killed":false,"level":"error","message":"❌ Failed to get network adapters: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq \"Up\"} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne \"WellKnown\"} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \"\" }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse(\"***************\").Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { \"\" }             Gateway = if($gateway) { $gateway.NextHop } else { \"\" }             DnsServers = if($dns) { $dns.ServerAddresses -join \",\" } else { \"\" }             DhcpEnabled = $ipAddress.PrefixOrigin -eq \"Dhcp\"             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:54\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                      ~\r\nYou must provide a value expression following the '-eq' operator.\r\nAt line:1 char:55\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                       ~~\r\nUnexpected token 'Up' in expression or statement.\r\nAt line:1 char:112\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:297\r\n+ ... s = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKno ...\r\n+                                                                  ~\r\nYou must provide a value expression following the '-ne' operator.\r\nAt line:1 char:298\r\n+ ... fig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKnown} | Sele ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token 'WellKnown' in expression or statement.\r\nAt line:1 char:793\r\n+ ... ss } else { \" } SubnetMask = if($ipAddress) { $prefixLength = $ipAddr ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nThe string is missing the terminator: \".\r\nAt line:1 char:791\r\n+ ...    CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \" } Sub ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:581\r\n+ ... tion SilentlyContinue                      [PSCustomObject]@{         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:76\r\n+ ... tAdapter | Where-Object {$_.Status -eq Up} | ForEach-Object {         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : ExpectedValueExpression\r\n \r\n","service":"vms-server","signal":null,"stack":"Error: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq \"Up\"} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne \"WellKnown\"} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \"\" }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse(\"***************\").Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { \"\" }             Gateway = if($gateway) { $gateway.NextHop } else { \"\" }             DnsServers = if($dns) { $dns.ServerAddresses -join \",\" } else { \"\" }             DhcpEnabled = $ipAddress.PrefixOrigin -eq \"Dhcp\"             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:54\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                      ~\r\nYou must provide a value expression following the '-eq' operator.\r\nAt line:1 char:55\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                       ~~\r\nUnexpected token 'Up' in expression or statement.\r\nAt line:1 char:112\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:297\r\n+ ... s = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKno ...\r\n+                                                                  ~\r\nYou must provide a value expression following the '-ne' operator.\r\nAt line:1 char:298\r\n+ ... fig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKnown} | Sele ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token 'WellKnown' in expression or statement.\r\nAt line:1 char:793\r\n+ ... ss } else { \" } SubnetMask = if($ipAddress) { $prefixLength = $ipAddr ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nThe string is missing the terminator: \".\r\nAt line:1 char:791\r\n+ ...    CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \" } Sub ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:581\r\n+ ... tion SilentlyContinue                      [PSCustomObject]@{         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:76\r\n+ ... tAdapter | Where-Object {$_.Status -eq Up} | ForEach-Object {         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : ExpectedValueExpression\r\n \r\n\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at ChildProcess.exithandler (node:child_process:414:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1101:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:304:5)","stderr":"At line:1 char:54\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                      ~\r\nYou must provide a value expression following the '-eq' operator.\r\nAt line:1 char:55\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                       ~~\r\nUnexpected token 'Up' in expression or statement.\r\nAt line:1 char:112\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:297\r\n+ ... s = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKno ...\r\n+                                                                  ~\r\nYou must provide a value expression following the '-ne' operator.\r\nAt line:1 char:298\r\n+ ... fig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKnown} | Sele ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token 'WellKnown' in expression or statement.\r\nAt line:1 char:793\r\n+ ... ss } else { \" } SubnetMask = if($ipAddress) { $prefixLength = $ipAddr ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nThe string is missing the terminator: \".\r\nAt line:1 char:791\r\n+ ...    CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \" } Sub ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:581\r\n+ ... tion SilentlyContinue                      [PSCustomObject]@{         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:76\r\n+ ... tAdapter | Where-Object {$_.Status -eq Up} | ForEach-Object {         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : ExpectedValueExpression\r\n \r\n","stdout":"","timestamp":"2025-08-04 11:52:28"}
{"level":"error","message":"❌ Failed to get current network info: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:52:28"}
{"level":"error","message":"❌ Error analyzing network: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:52:28"}
{"level":"error","message":"❌ Failed to start Hybrid Network Service: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:52:28"}
{"level":"error","message":"❌ Failed to initialize Network Integration System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:52:28"}
{"level":"error","message":"❌ Failed to initialize Hybrid Network System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:52:28"}
{"cmd":"powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq \"Up\"} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne \"WellKnown\"} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \"\" }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse(\"***************\").Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { \"\" }             Gateway = if($gateway) { $gateway.NextHop } else { \"\" }             DnsServers = if($dns) { $dns.ServerAddresses -join \",\" } else { \"\" }             DhcpEnabled = $ipAddress.PrefixOrigin -eq \"Dhcp\"             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"","code":1,"killed":false,"level":"error","message":"❌ Failed to get network adapters: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq \"Up\"} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne \"WellKnown\"} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \"\" }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse(\"***************\").Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { \"\" }             Gateway = if($gateway) { $gateway.NextHop } else { \"\" }             DnsServers = if($dns) { $dns.ServerAddresses -join \",\" } else { \"\" }             DhcpEnabled = $ipAddress.PrefixOrigin -eq \"Dhcp\"             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:54\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                      ~\r\nYou must provide a value expression following the '-eq' operator.\r\nAt line:1 char:55\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                       ~~\r\nUnexpected token 'Up' in expression or statement.\r\nAt line:1 char:112\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:297\r\n+ ... s = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKno ...\r\n+                                                                  ~\r\nYou must provide a value expression following the '-ne' operator.\r\nAt line:1 char:298\r\n+ ... fig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKnown} | Sele ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token 'WellKnown' in expression or statement.\r\nAt line:1 char:793\r\n+ ... ss } else { \" } SubnetMask = if($ipAddress) { $prefixLength = $ipAddr ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nThe string is missing the terminator: \".\r\nAt line:1 char:791\r\n+ ...    CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \" } Sub ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:581\r\n+ ... tion SilentlyContinue                      [PSCustomObject]@{         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:76\r\n+ ... tAdapter | Where-Object {$_.Status -eq Up} | ForEach-Object {         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : ExpectedValueExpression\r\n \r\n","service":"vms-server","signal":null,"stack":"Error: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq \"Up\"} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne \"WellKnown\"} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \"\" }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse(\"***************\").Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { \"\" }             Gateway = if($gateway) { $gateway.NextHop } else { \"\" }             DnsServers = if($dns) { $dns.ServerAddresses -join \",\" } else { \"\" }             DhcpEnabled = $ipAddress.PrefixOrigin -eq \"Dhcp\"             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:54\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                      ~\r\nYou must provide a value expression following the '-eq' operator.\r\nAt line:1 char:55\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                       ~~\r\nUnexpected token 'Up' in expression or statement.\r\nAt line:1 char:112\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:297\r\n+ ... s = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKno ...\r\n+                                                                  ~\r\nYou must provide a value expression following the '-ne' operator.\r\nAt line:1 char:298\r\n+ ... fig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKnown} | Sele ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token 'WellKnown' in expression or statement.\r\nAt line:1 char:793\r\n+ ... ss } else { \" } SubnetMask = if($ipAddress) { $prefixLength = $ipAddr ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nThe string is missing the terminator: \".\r\nAt line:1 char:791\r\n+ ...    CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \" } Sub ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:581\r\n+ ... tion SilentlyContinue                      [PSCustomObject]@{         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:76\r\n+ ... tAdapter | Where-Object {$_.Status -eq Up} | ForEach-Object {         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : ExpectedValueExpression\r\n \r\n\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at ChildProcess.exithandler (node:child_process:414:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1101:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:304:5)","stderr":"At line:1 char:54\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                      ~\r\nYou must provide a value expression following the '-eq' operator.\r\nAt line:1 char:55\r\n+          Get-NetAdapter | Where-Object {$_.Status -eq Up} | ForEach-O ...\r\n+                                                       ~~\r\nUnexpected token 'Up' in expression or statement.\r\nAt line:1 char:112\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:297\r\n+ ... s = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKno ...\r\n+                                                                  ~\r\nYou must provide a value expression following the '-ne' operator.\r\nAt line:1 char:298\r\n+ ... fig.IPv4Address | Where-Object {$_.PrefixOrigin -ne WellKnown} | Sele ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token 'WellKnown' in expression or statement.\r\nAt line:1 char:793\r\n+ ... ss } else { \" } SubnetMask = if($ipAddress) { $prefixLength = $ipAddr ...\r\n+                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nThe string is missing the terminator: \".\r\nAt line:1 char:791\r\n+ ...    CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { \" } Sub ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:581\r\n+ ... tion SilentlyContinue                      [PSCustomObject]@{         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\nAt line:1 char:76\r\n+ ... tAdapter | Where-Object {$_.Status -eq Up} | ForEach-Object {         ...\r\n+                                                                 ~\r\nMissing closing '}' in statement block or type definition.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : ExpectedValueExpression\r\n \r\n","stdout":"","timestamp":"2025-08-04 11:53:33"}
{"level":"error","message":"❌ Failed to get current network info: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:53:33"}
{"level":"error","message":"❌ Error analyzing network: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:53:33"}
{"level":"error","message":"❌ Failed to start Hybrid Network Service: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:53:33"}
{"level":"error","message":"❌ Failed to initialize Network Integration System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:53:33"}
{"level":"error","message":"❌ Failed to initialize Hybrid Network System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:53:33"}
{"cmd":"powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"","code":1,"killed":false,"level":"error","message":"❌ Failed to get network adapters: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n","service":"vms-server","signal":null,"stack":"Error: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at ChildProcess.exithandler (node:child_process:414:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1101:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:304:5)","stderr":"At line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n","stdout":"","timestamp":"2025-08-04 11:57:48"}
{"level":"error","message":"❌ Failed to get current network info: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:57:48"}
{"level":"error","message":"❌ Error analyzing network: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:57:48"}
{"level":"error","message":"❌ Failed to start Hybrid Network Service: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:57:48"}
{"level":"error","message":"❌ Failed to initialize Network Integration System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:57:48"}
{"level":"error","message":"❌ Failed to initialize Hybrid Network System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 11:57:48"}
{"level":"error","message":"🚫 REJECTING WebSocket connection - no valid session found","service":"vms-server","timestamp":"2025-08-04 12:13:29"}
{"cmd":"powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"","code":1,"killed":false,"level":"error","message":"❌ Failed to get network adapters: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n","service":"vms-server","signal":null,"stack":"Error: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at ChildProcess.exithandler (node:child_process:414:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1101:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:304:5)","stderr":"At line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n","stdout":"","timestamp":"2025-08-04 12:27:37"}
{"level":"error","message":"❌ Failed to get current network info: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 12:27:37"}
{"level":"error","message":"❌ Error analyzing network: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 12:27:37"}
{"level":"error","message":"❌ Failed to start Hybrid Network Service: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 12:27:37"}
{"level":"error","message":"❌ Failed to initialize Network Integration System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 12:27:37"}
{"level":"error","message":"❌ Failed to initialize Hybrid Network System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 12:27:37"}
{"cmd":"powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"","code":1,"killed":false,"level":"error","message":"❌ Failed to get network adapters: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n","service":"vms-server","signal":null,"stack":"Error: Command failed: powershell -Command \"         Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | ForEach-Object {           $adapter = $_           $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex -ErrorAction SilentlyContinue           $ipAddress = $ipConfig.IPv4Address | Where-Object {$_.PrefixOrigin -ne 'WellKnown'} | Select-Object -First 1           $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1           $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue                      [PSCustomObject]@{             Name = $adapter.Name             Description = $adapter.InterfaceDescription             PhysicalAddress = $adapter.MacAddress             CurrentIP = if($ipAddress) { $ipAddress.IPAddress } else { '' }             SubnetMask = if($ipAddress) {                $prefixLength = $ipAddress.PrefixLength               $mask = [System.Net.IPAddress]::Parse('***************').Address               $mask = $mask -shl (32 - $prefixLength)               [System.Net.IPAddress]::new($mask).ToString()             } else { '' }             Gateway = if($gateway) { $gateway.NextHop } else { '' }             DnsServers = if($dns) { $dns.ServerAddresses -join ',' } else { '' }             DhcpEnabled = $ipAddress.PrefixOrigin -eq 'Dhcp'             InterfaceIndex = $adapter.InterfaceIndex           }         } | ConvertTo-Json -Depth 3       \"\nAt line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at ChildProcess.exithandler (node:child_process:414:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1101:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:304:5)","stderr":"At line:1 char:114\r\n+ ...  ForEach-Object {           $adapter = $_           $ipConfig = Get-N ...\r\n+                                                         ~~~~~~~~~\r\nUnexpected token '$ipConfig' in expression or statement.\r\nAt line:1 char:913\r\n+ ...   $prefixLength = $ipAddress.PrefixLength               $mask = [Syst ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:992\r\n+ ... ddress]::Parse('***************').Address               $mask = $mask ...\r\n+                                                             ~~~~~\r\nUnexpected token '$mask' in expression or statement.\r\nAt line:1 char:1046\r\n+ ... 32 - $prefixLength)               [System.Net.IPAddress]::new($mask). ...\r\n+                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~\r\nUnexpected token '[System.Net.IPAddress]::new' in expression or statement.\r\n    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException\r\n    + FullyQualifiedErrorId : UnexpectedToken\r\n \r\n","stdout":"","timestamp":"2025-08-04 13:09:12"}
{"level":"error","message":"❌ Failed to get current network info: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 13:09:12"}
{"level":"error","message":"❌ Error analyzing network: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 13:09:12"}
{"level":"error","message":"❌ Failed to start Hybrid Network Service: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 13:09:12"}
{"level":"error","message":"❌ Failed to initialize Network Integration System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 13:09:12"}
{"level":"error","message":"❌ Failed to initialize Hybrid Network System: No primary network adapter found","service":"vms-server","stack":"Error: No primary network adapter found\n    at StaticIPAssignmentService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\static-ip-assignment-service.js:471:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async HybridNetworkService.getCurrentNetworkInfo (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:322:16)\n    at async HybridNetworkService.analyzeCurrentNetwork (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:96:33)\n    at async HybridNetworkService.start (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\hybrid-network-service.js:47:13)\n    at async NetworkIntegrationService.initialize (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\services\\network-integration-service.js:41:13)\n    at async Server.<anonymous> (C:\\Users\\<USER>\\Desktop\\VMS-PRODUCTION\\Server\\dist\\index.js:352:17)","timestamp":"2025-08-04 13:09:12"}
