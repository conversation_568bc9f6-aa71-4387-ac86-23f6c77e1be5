"use strict";
/**
 * Static IP Assignment Service
 * Handles automated static IP assignment with network analysis and Windows configuration
 * Provides intelligent IP selection and conflict resolution
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.staticIPAssignmentService = exports.StaticIPAssignmentService = void 0;
const child_process_1 = require("child_process");
const util_1 = require("util");
const os_1 = __importDefault(require("os"));
const logger_js_1 = require("../utils/logger.js");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
class StaticIPAssignmentService {
    _isWindows = os_1.default.platform() === 'win32';
    _backupConfigs = new Map();
    /**
     * Analyze current network configuration
     */
    async analyzeNetwork() {
        try {
            logger_js_1.logger.info('🔍 Analyzing network configuration...');
            if (!this._isWindows) {
                throw new Error('Static IP assignment currently only supported on Windows');
            }
            // Get all network adapters
            const adapters = await this.getNetworkAdapters();
            // Find primary adapter
            const primaryAdapter = this.findPrimaryAdapter(adapters);
            if (!primaryAdapter) {
                throw new Error('No suitable primary network adapter found');
            }
            // Analyze network range
            const networkRange = this.analyzeNetworkRange(primaryAdapter);
            // Find recommended static IPs
            const recommendedStaticIPs = await this.findRecommendedStaticIPs(networkRange);
            // Check for IP conflicts
            const conflictingIPs = await this.findConflictingIPs(recommendedStaticIPs);
            const analysis = {
                adapters,
                primaryAdapter,
                networkRange,
                recommendedStaticIPs: recommendedStaticIPs.filter(ip => !conflictingIPs.includes(ip)),
                conflictingIPs
            };
            logger_js_1.logger.info(`✅ Network analysis complete:`);
            logger_js_1.logger.info(`   Primary Adapter: ${primaryAdapter.name}`);
            logger_js_1.logger.info(`   Current IP: ${primaryAdapter.currentIP}`);
            logger_js_1.logger.info(`   Network Range: ${networkRange?.network}/${this.cidrFromSubnetMask(networkRange?.subnetMask || '')}`);
            logger_js_1.logger.info(`   Recommended IPs: ${analysis.recommendedStaticIPs.slice(0, 3).join(', ')}...`);
            return analysis;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Network analysis failed:', error);
            throw error;
        }
    }
    /**
     * Find optimal static IP configuration
     */
    async findOptimalStaticIP() {
        try {
            const analysis = await this.analyzeNetwork();
            if (!analysis.primaryAdapter || analysis.recommendedStaticIPs.length === 0) {
                logger_js_1.logger.warn('⚠️ No suitable static IP configuration found');
                return null;
            }
            // Select the best IP from recommendations
            const targetIP = this.selectBestStaticIP(analysis.recommendedStaticIPs, analysis.primaryAdapter.currentIP);
            return {
                targetIP,
                subnetMask: analysis.primaryAdapter.subnetMask,
                gateway: analysis.primaryAdapter.gateway,
                dnsServers: analysis.primaryAdapter.dnsServers,
                adapterName: analysis.primaryAdapter.name
            };
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to find optimal static IP:', error);
            return null;
        }
    }
    /**
     * Assign static IP to network adapter
     */
    async assignStaticIP(config) {
        try {
            logger_js_1.logger.info(`🔧 Assigning static IP ${config.targetIP} to adapter ${config.adapterName}...`);
            // Backup current configuration
            const backupConfig = await this.backupAdapterConfiguration(config.adapterName);
            // Test IP availability
            const isAvailable = await this.testIPAvailability(config.targetIP);
            if (!isAvailable) {
                return {
                    success: false,
                    assignedIP: null,
                    adapterName: config.adapterName,
                    previousConfig: backupConfig,
                    error: `IP ${config.targetIP} is already in use`,
                    rollbackAvailable: true
                };
            }
            // Assign static IP
            await this.setStaticIPConfiguration(config);
            // Verify assignment
            const verificationResult = await this.verifyStaticIPAssignment(config.targetIP, config.adapterName);
            if (verificationResult.success) {
                logger_js_1.logger.info(`✅ Static IP ${config.targetIP} assigned successfully`);
                return {
                    success: true,
                    assignedIP: config.targetIP,
                    adapterName: config.adapterName,
                    previousConfig: backupConfig,
                    error: null,
                    rollbackAvailable: true
                };
            }
            else {
                // Assignment failed, attempt rollback
                await this.rollbackConfiguration(config.adapterName, backupConfig);
                return {
                    success: false,
                    assignedIP: null,
                    adapterName: config.adapterName,
                    previousConfig: backupConfig,
                    error: verificationResult.error || 'Static IP assignment verification failed',
                    rollbackAvailable: false
                };
            }
        }
        catch (error) {
            logger_js_1.logger.error('❌ Static IP assignment failed:', error);
            return {
                success: false,
                assignedIP: null,
                adapterName: config.adapterName,
                previousConfig: null,
                error: error instanceof Error ? error.message : 'Unknown error',
                rollbackAvailable: false
            };
        }
    }
    /**
     * Get all network adapters
     */
    async getNetworkAdapters() {
        try {
            // Use Node.js native os module instead of PowerShell
            const os = require('os');
            const networkInterfaces = os.networkInterfaces();
            const adapters = [];
            for (const [name, interfaces] of Object.entries(networkInterfaces)) {
                if (!interfaces)
                    continue;
                // Find the primary IPv4 interface that's not internal
                const ipv4Interface = interfaces.find((iface) => iface.family === 'IPv4' &&
                    !iface.internal &&
                    iface.address !== '127.0.0.1');
                if (ipv4Interface) {
                    // Calculate subnet mask from CIDR
                    const prefixLength = ipv4Interface.cidr ? parseInt(ipv4Interface.cidr.split('/')[1]) : 24;
                    const subnetMask = this.cidrToSubnetMask(prefixLength);
                    adapters.push({
                        name: name,
                        description: `Network Interface: ${name}`,
                        physicalAddress: ipv4Interface.mac || '',
                        currentIP: ipv4Interface.address,
                        subnetMask: subnetMask,
                        gateway: this.getDefaultGateway(ipv4Interface.address, subnetMask),
                        dnsServers: ['*******', '*******'],
                        dhcpEnabled: true,
                        isActive: true,
                        interfaceIndex: 1
                    });
                }
            }
            if (adapters.length === 0) {
                logger_js_1.logger.warn('No active network adapters found, creating fallback adapter');
                // Create a fallback adapter for localhost
                adapters.push({
                    name: 'Localhost',
                    description: 'Localhost fallback adapter',
                    physicalAddress: '00:00:00:00:00:00',
                    currentIP: '127.0.0.1',
                    subnetMask: '*********',
                    gateway: '127.0.0.1',
                    dnsServers: ['*******', '*******'],
                    dhcpEnabled: false,
                    isActive: true,
                    interfaceIndex: 0
                });
            }
            logger_js_1.logger.info(`Found ${adapters.length} network adapter(s)`);
            return adapters;
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to get network adapters:', error);
            // Return fallback adapter
            return [{
                    name: 'Fallback',
                    description: 'Fallback network adapter',
                    physicalAddress: '00:00:00:00:00:00',
                    currentIP: '127.0.0.1',
                    subnetMask: '*********',
                    gateway: '127.0.0.1',
                    dnsServers: ['*******', '*******'],
                    dhcpEnabled: false,
                    isActive: true,
                    interfaceIndex: 0
                }];
        }
    }
    cidrToSubnetMask(prefixLength) {
        const mask = (0xFFFFFFFF << (32 - prefixLength)) >>> 0;
        return [
            (mask >>> 24) & 0xFF,
            (mask >>> 16) & 0xFF,
            (mask >>> 8) & 0xFF,
            mask & 0xFF
        ].join('.');
    }
    getDefaultGateway(ip, subnetMask) {
        // Simple gateway calculation - assume it's the first IP in the subnet
        const ipParts = ip.split('.').map(Number);
        const maskParts = subnetMask.split('.').map(Number);
        const networkParts = ipParts.map((part, index) => part & maskParts[index]);
        networkParts[3] = networkParts[3] + 1; // Gateway is typically .1
        return networkParts.join('.');
    }
    /**
     * Find primary network adapter
     */
    findPrimaryAdapter(adapters) {
        // Prefer Ethernet over WiFi
        const ethernetAdapter = adapters.find(adapter => adapter.description.toLowerCase().includes('ethernet') &&
            adapter.gateway &&
            adapter.currentIP);
        if (ethernetAdapter) {
            return ethernetAdapter;
        }
        // Fallback to WiFi
        const wifiAdapter = adapters.find(adapter => (adapter.description.toLowerCase().includes('wireless') ||
            adapter.description.toLowerCase().includes('wi-fi')) &&
            adapter.gateway &&
            adapter.currentIP);
        if (wifiAdapter) {
            return wifiAdapter;
        }
        // Fallback to any adapter with gateway
        return adapters.find(adapter => adapter.gateway && adapter.currentIP) || null;
    }
    /**
     * Analyze network range for static IP recommendations
     */
    analyzeNetworkRange(adapter) {
        try {
            const ip = adapter.currentIP;
            const mask = adapter.subnetMask;
            const gateway = adapter.gateway;
            if (!ip || !mask || !gateway) {
                return null;
            }
            // Calculate network address
            const ipParts = ip.split('.').map(Number);
            const maskParts = mask.split('.').map(Number);
            const networkParts = ipParts.map((part, index) => part & maskParts[index]);
            const network = networkParts.join('.');
            // Calculate broadcast address
            const broadcastParts = ipParts.map((part, index) => part | (255 - maskParts[index]));
            const broadcastAddress = broadcastParts.join('.');
            // Define available range (avoid first 10 and last 10 addresses)
            const availableStart = [...networkParts];
            availableStart[3] = Math.max(availableStart[3] + 10, 100); // Start from .100 or network+10
            const availableEnd = [...broadcastParts];
            availableEnd[3] = Math.max(availableEnd[3] - 10, 200); // End at broadcast-10 or .200
            return {
                network,
                subnetMask: mask,
                gateway,
                broadcastAddress,
                availableRange: {
                    start: availableStart.join('.'),
                    end: availableEnd.join('.')
                }
            };
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error analyzing network range:', error);
            return null;
        }
    }
    /**
     * Find recommended static IP addresses
     */
    async findRecommendedStaticIPs(networkRange) {
        if (!networkRange) {
            return [];
        }
        const recommendations = [];
        const startParts = networkRange.availableRange.start.split('.').map(Number);
        const endParts = networkRange.availableRange.end.split('.').map(Number);
        // Generate IP addresses in the available range
        for (let i = startParts[3]; i <= endParts[3] && recommendations.length < 20; i++) {
            const ip = `${startParts[0]}.${startParts[1]}.${startParts[2]}.${i}`;
            recommendations.push(ip);
        }
        return recommendations;
    }
    /**
     * Find conflicting IP addresses
     */
    async findConflictingIPs(candidateIPs) {
        const conflicts = [];
        // Test each candidate IP for conflicts
        for (const ip of candidateIPs.slice(0, 10)) { // Test first 10 to avoid long delays
            try {
                const isAvailable = await this.testIPAvailability(ip);
                if (!isAvailable) {
                    conflicts.push(ip);
                }
            }
            catch {
                // If we can't test, assume it might be in use
                conflicts.push(ip);
            }
        }
        return conflicts;
    }
    /**
     * Select best static IP from recommendations
     */
    selectBestStaticIP(recommendations, currentIP) {
        // Prefer IPs close to current IP but not the same
        const currentParts = currentIP.split('.').map(Number);
        const currentLastOctet = currentParts[3];
        // Find IP close to current but different
        const preferred = recommendations.find(ip => {
            const parts = ip.split('.').map(Number);
            const lastOctet = parts[3];
            return Math.abs(lastOctet - currentLastOctet) >= 5 && Math.abs(lastOctet - currentLastOctet) <= 20;
        });
        return preferred || recommendations[0];
    }
    /**
     * Test IP availability using ping
     */
    async testIPAvailability(ip) {
        try {
            const command = `ping -n 1 -w 1000 ${ip}`;
            await execAsync(command);
            return false; // If ping succeeds, IP is in use
        }
        catch {
            return true; // If ping fails, IP is available
        }
    }
    /**
     * Backup adapter configuration
     */
    async backupAdapterConfiguration(adapterName) {
        try {
            const command = `
        $adapter = Get-NetAdapter -Name "${adapterName}"
        $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex
        $ipAddress = $ipConfig.IPv4Address | Select-Object -First 1
        $gateway = $ipConfig.IPv4DefaultGateway | Select-Object -First 1
        $dns = Get-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4
        
        [PSCustomObject]@{
          AdapterName = $adapter.Name
          InterfaceIndex = $adapter.InterfaceIndex
          CurrentIP = $ipAddress.IPAddress
          PrefixLength = $ipAddress.PrefixLength
          Gateway = $gateway.NextHop
          DnsServers = $dns.ServerAddresses
          DhcpEnabled = $ipAddress.PrefixOrigin -eq "Dhcp"
        } | ConvertTo-Json -Depth 2
      `;
            const { stdout } = await execAsync(`powershell -Command "${command.replace(/\n/g, ' ')}"`);
            const backup = JSON.parse(stdout);
            this._backupConfigs.set(adapterName, backup);
            logger_js_1.logger.info(`💾 Backed up configuration for adapter: ${adapterName}`);
            return backup;
        }
        catch (error) {
            logger_js_1.logger.error(`❌ Failed to backup configuration for ${adapterName}:`, error);
            return null;
        }
    }
    /**
     * Set static IP configuration
     */
    async setStaticIPConfiguration(config) {
        try {
            const prefixLength = this.cidrFromSubnetMask(config.subnetMask);
            const dnsServersStr = config.dnsServers.join('","');
            const command = `
        $adapter = Get-NetAdapter -Name "${config.adapterName}"
        
        # Remove existing IP configuration
        Remove-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
        Remove-NetRoute -InterfaceIndex $adapter.InterfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
        
        # Set static IP
        New-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -IPAddress "${config.targetIP}" -PrefixLength ${prefixLength} -DefaultGateway "${config.gateway}"
        
        # Set DNS servers
        Set-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -ServerAddresses "${dnsServersStr}"
      `;
            await execAsync(`powershell -Command "${command.replace(/\n/g, ' ')}"`);
            logger_js_1.logger.info(`🔧 Static IP configuration applied to ${config.adapterName}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to set static IP configuration:', error);
            throw error;
        }
    }
    /**
     * Verify static IP assignment
     */
    async verifyStaticIPAssignment(targetIP, adapterName) {
        try {
            // Wait a moment for configuration to take effect
            await new Promise(resolve => setTimeout(resolve, 3000));
            const command = `
        $adapter = Get-NetAdapter -Name "${adapterName}"
        $ipConfig = Get-NetIPConfiguration -InterfaceIndex $adapter.InterfaceIndex
        $ipAddress = $ipConfig.IPv4Address | Select-Object -First 1
        $ipAddress.IPAddress
      `;
            const { stdout } = await execAsync(`powershell -Command "${command.replace(/\n/g, ' ')}"`);
            const assignedIP = stdout.trim();
            if (assignedIP === targetIP) {
                return { success: true };
            }
            else {
                return { success: false, error: `Expected ${targetIP}, got ${assignedIP}` };
            }
        }
        catch (error) {
            return { success: false, error: error instanceof Error ? error.message : 'Verification failed' };
        }
    }
    /**
     * Rollback configuration
     */
    async rollbackConfiguration(adapterName, backupConfig) {
        try {
            if (!backupConfig) {
                logger_js_1.logger.warn(`⚠️ No backup configuration available for ${adapterName}`);
                return;
            }
            logger_js_1.logger.info(`🔄 Rolling back configuration for ${adapterName}...`);
            if (backupConfig.DhcpEnabled) {
                // Restore DHCP
                const command = `
          $adapter = Get-NetAdapter -Name "${adapterName}"
          Remove-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
          Remove-NetRoute -InterfaceIndex $adapter.InterfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
          Set-NetIPInterface -InterfaceIndex $adapter.InterfaceIndex -Dhcp Enabled
        `;
                await execAsync(`powershell -Command "${command.replace(/\n/g, ' ')}"`);
            }
            else {
                // Restore static configuration
                const dnsServersStr = backupConfig.DnsServers.join('","');
                const command = `
          $adapter = Get-NetAdapter -Name "${adapterName}"
          Remove-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
          Remove-NetRoute -InterfaceIndex $adapter.InterfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
          New-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -IPAddress "${backupConfig.CurrentIP}" -PrefixLength ${backupConfig.PrefixLength} -DefaultGateway "${backupConfig.Gateway}"
          Set-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -ServerAddresses "${dnsServersStr}"
        `;
                await execAsync(`powershell -Command "${command.replace(/\n/g, ' ')}"`);
            }
            logger_js_1.logger.info(`✅ Configuration rolled back for ${adapterName}`);
        }
        catch (error) {
            logger_js_1.logger.error(`❌ Failed to rollback configuration for ${adapterName}:`, error);
        }
    }
    /**
     * Convert subnet mask to CIDR notation
     */
    cidrFromSubnetMask(subnetMask) {
        const maskParts = subnetMask.split('.').map(Number);
        let cidr = 0;
        for (const part of maskParts) {
            cidr += (part >>> 0).toString(2).split('1').length - 1;
        }
        return cidr;
    }
    /**
     * Get current network information
     */
    async getCurrentNetworkInfo() {
        try {
            const adapters = await this.getNetworkAdapters();
            const primaryAdapter = this.findPrimaryAdapter(adapters);
            if (!primaryAdapter) {
                throw new Error('No primary network adapter found');
            }
            return {
                currentIP: primaryAdapter.currentIP,
                adapterName: primaryAdapter.name,
                subnetMask: primaryAdapter.subnetMask,
                gateway: primaryAdapter.gateway,
                dnsServers: primaryAdapter.dnsServers
            };
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to get current network info:', error);
            throw error;
        }
    }
}
exports.StaticIPAssignmentService = StaticIPAssignmentService;
// Export singleton instance
exports.staticIPAssignmentService = new StaticIPAssignmentService();
//# sourceMappingURL=static-ip-assignment-service.js.map