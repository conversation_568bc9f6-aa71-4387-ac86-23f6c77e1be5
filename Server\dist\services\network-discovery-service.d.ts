/**
 * Network Discovery Service
 * Implements UDP broadcast system for dynamic IP discovery and client auto-configuration
 * Handles server announcement and client discovery for automated deployment
 */
export interface NetworkDiscoveryConfig {
    broadcastPort: number;
    discoveryPort: number;
    serviceName: string;
    announceInterval: number;
    enabled: boolean;
}
export interface ServerAnnouncement {
    serviceName: string;
    serverIP: string;
    serverPort: number;
    version: string;
    timestamp: number;
    capabilities: string[];
    deploymentMode: string;
}
export declare class NetworkDiscoveryService {
    private broadcastSocket;
    private discoverySocket;
    private announceTimer;
    private isRunning;
    private config;
    private serverInfo;
    constructor(serverPort?: number);
    /**
     * Start network discovery service
     */
    start(): Promise<void>;
    /**
     * Stop network discovery service
     */
    stop(): Promise<void>;
    /**
     * Start broadcast socket for server announcements
     */
    private startBroadcastSocket;
    /**
     * Start discovery socket for client queries
     */
    private startDiscoverySocket;
    /**
     * Handle discovery request from client
     */
    private handleDiscoveryRequest;
    /**
     * Start periodic server announcements
     */
    private startPeriodicAnnouncements;
    /**
     * Broadcast server announcement
     */
    private broadcastServerAnnouncement;
    /**
     * Get local IP address
     */
    private getLocalIPAddress;
    /**
     * Get broadcast addresses for all network interfaces
     */
    private getBroadcastAddresses;
    /**
     * Update server configuration
     */
    updateConfig(newConfig: Partial<NetworkDiscoveryConfig>): void;
    /**
     * Get current server information
     */
    getServerInfo(): ServerAnnouncement;
    /**
     * Check if service is running
     */
    isServiceRunning(): boolean;
    /**
     * Get network statistics
     */
    getNetworkStats(): any;
}
export declare const networkDiscoveryService: NetworkDiscoveryService;
