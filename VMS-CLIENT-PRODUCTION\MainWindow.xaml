<Window x:Class="VMSClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="VMS CLIENT"
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        MinHeight="500" MinWidth="700"
        Background="#F8F9FA">
    
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="32"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1565C0"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
        </Style>
        
        <Style x:Key="SubHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Foreground" Value="#424242"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,30"/>
        </Style>
        
        <Style x:Key="StatusStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,15"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style x:Key="ActionButton" TargetType="Button">
            <Setter Property="Background" Value="#1565C0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="25,12"/>
            <Setter Property="Margin" Value="15,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="MinWidth" Value="200"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#BDBDBD"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="InfoCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="0,10"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header Section -->
        <StackPanel Grid.Row="0" Margin="30,20,30,0">
            <TextBlock Text="VMS CLIENT" Style="{StaticResource HeaderStyle}"/>
            <TextBlock Text="Voucher Management System Client" Style="{StaticResource SubHeaderStyle}"/>
        </StackPanel>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <StackPanel Margin="40,0,40,20">
                
                <!-- Connection Status Card -->
                <Border Style="{StaticResource InfoCardStyle}">
                    <StackPanel>
                        <TextBlock Text="Connection Status" FontSize="18" FontWeight="Bold" 
                                  Foreground="#1565C0" Margin="0,0,0,20"/>
                        
                        <TextBlock x:Name="StatusText" Text="Searching for VMS server..." 
                                  Style="{StaticResource StatusStyle}" Foreground="#FF9800"/>
                        
                        <ProgressBar x:Name="ConnectionProgress" Height="8" Margin="0,20,0,0" 
                                    Background="#E0E0E0" Foreground="#1565C0" IsIndeterminate="True"/>
                    </StackPanel>
                </Border>
                
                <!-- Server Information Card -->
                <Border Style="{StaticResource InfoCardStyle}" x:Name="ServerInfoCard" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="VMS Server Found" FontSize="18" FontWeight="Bold" 
                                  Foreground="#1565C0" Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Server:" 
                                      FontWeight="SemiBold" Margin="0,8,20,8" FontSize="14"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="ServerAddressText" 
                                      Text="Not detected" Margin="0,8" FontSize="14"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Version:" 
                                      FontWeight="SemiBold" Margin="0,8,20,8" FontSize="14"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="ServerVersionText" 
                                      Text="Unknown" Margin="0,8" FontSize="14"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Status:" 
                                      FontWeight="SemiBold" Margin="0,8,20,8" FontSize="14"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="ConnectionStatusText" 
                                      Text="Ready" Foreground="#4CAF50" Margin="0,8" FontSize="14"/>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Discovery Information Card -->
                <Border Style="{StaticResource InfoCardStyle}">
                    <StackPanel>
                        <TextBlock Text="Network Discovery" FontSize="18" FontWeight="Bold" 
                                  Foreground="#1565C0" Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Method:" 
                                      FontWeight="SemiBold" Margin="0,8,20,8" FontSize="14"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="Automatic Network Scan" 
                                      Margin="0,8" FontSize="14"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Scan Status:" 
                                      FontWeight="SemiBold" Margin="0,8,20,8" FontSize="14"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="ScanStatusText" 
                                      Text="Active" Foreground="#4CAF50" Margin="0,8" FontSize="14"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Last Scan:" 
                                      FontWeight="SemiBold" Margin="0,8,20,8" FontSize="14"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="LastScanText" 
                                      Text="Just now" Margin="0,8" FontSize="14"/>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,30">
                    <Button x:Name="ConnectButton" Content="Connect to VMS" 
                           Style="{StaticResource ActionButton}" Click="ConnectButton_Click" IsEnabled="False"/>
                    <Button x:Name="RefreshButton" Content="Refresh Scan" 
                           Style="{StaticResource ActionButton}" Click="RefreshButton_Click"/>
                </StackPanel>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F0F0F0" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" 
               Padding="25,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="VMS CLIENT" FontWeight="Bold" FontSize="12" Foreground="#1565C0" Margin="0,0,10,0"/>
                <TextBlock Text="•" FontSize="12" Foreground="#9E9E9E" Margin="0,0,10,0"/>
                <TextBlock Text="Production Ready" FontSize="12" Foreground="#4CAF50" Margin="0,0,10,0"/>
                <TextBlock Text="•" FontSize="12" Foreground="#9E9E9E" Margin="0,0,10,0"/>
                <TextBlock Text="Network Discovery Active" FontSize="12" Foreground="#9E9E9E"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
