{"version": 3, "file": "TypeOrValueSpecifier.js", "sourceRoot": "", "sources": ["../src/TypeOrValueSpecifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA,oDAgCC;AAjMD,sDAAwC;AAExC,uFAAoF;AACpF,mFAAgF;AAChF,iFAA8E;AAC9E,uHAAoH;AA6DvG,QAAA,2BAA2B,GAAG;IACzC,KAAK,EAAE;QACL,KAAK,EAAE;YACL;gBACE,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,IAAI,EAAE;wBACJ,IAAI,EAAE,CAAC,MAAM,CAAC;wBACd,IAAI,EAAE,QAAQ;qBACf;oBACD,IAAI,EAAE;wBACJ,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;6BACf;4BACD;gCACE,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;iCACf;gCACD,QAAQ,EAAE,CAAC;gCACX,IAAI,EAAE,OAAO;gCACb,WAAW,EAAE,IAAI;6BAClB;yBACF;qBACF;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;qBACf;iBACF;gBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC1B,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,IAAI,EAAE;wBACJ,IAAI,EAAE,CAAC,KAAK,CAAC;wBACb,IAAI,EAAE,QAAQ;qBACf;oBACD,IAAI,EAAE;wBACJ,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;6BACf;4BACD;gCACE,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;iCACf;gCACD,QAAQ,EAAE,CAAC;gCACX,IAAI,EAAE,OAAO;gCACb,WAAW,EAAE,IAAI;6BAClB;yBACF;qBACF;iBACF;gBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC1B,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,IAAI,EAAE;wBACJ,IAAI,EAAE,CAAC,SAAS,CAAC;wBACjB,IAAI,EAAE,QAAQ;qBACf;oBACD,IAAI,EAAE;wBACJ,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;6BACf;4BACD;gCACE,KAAK,EAAE;oCACL,IAAI,EAAE,QAAQ;iCACf;gCACD,QAAQ,EAAE,CAAC;gCACX,IAAI,EAAE,OAAO;gCACb,WAAW,EAAE,IAAI;6BAClB;yBACF;qBACF;oBACD,OAAO,EAAE;wBACP,IAAI,EAAE,QAAQ;qBACf;iBACF;gBACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC;gBACrC,IAAI,EAAE,QAAQ;aACf;SACF;KACF;IACD,IAAI,EAAE,OAAO;CACiB,CAAC;AAEjC,SAAgB,oBAAoB,CAClC,IAAa,EACb,SAA+B,EAC/B,OAAmB;IAEnB,IAAI,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAClC,OAAO,IAAA,2CAAoB,EAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC/C,CAAC;IACD,IAAI,CAAC,IAAA,2CAAoB,EAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC;IACpD,MAAM,YAAY,GAAG,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC;IACrD,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CACtD,WAAW,CAAC,aAAa,EAAE,CAC5B,CAAC;IACF,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;QACvB,KAAK,MAAM;YACT,OAAO,IAAA,uCAAkB,EAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACvE,KAAK,KAAK;YACR,OAAO,IAAA,qCAAiB,EAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACtD,KAAK,SAAS;YACZ,OAAO,IAAA,2EAAoC,EACzC,SAAS,CAAC,OAAO,EACjB,YAAY,EACZ,gBAAgB,EAChB,OAAO,CACR,CAAC;IACN,CAAC;AACH,CAAC;AAEM,MAAM,wBAAwB,GAAG,CACtC,IAAa,EACb,aAAqC,EAAE,EACvC,OAAmB,EACV,EAAE,CACX,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;AALlE,QAAA,wBAAwB,4BAK0C"}