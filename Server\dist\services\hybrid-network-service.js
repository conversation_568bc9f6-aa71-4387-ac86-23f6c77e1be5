"use strict";
/**
 * Hybrid Network Management Service
 * Combines static IP assignment with network discovery backup
 * Provides bulletproof network handling for production deployment
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.hybridNetworkService = exports.HybridNetworkService = void 0;
const events_1 = require("events");
const network_discovery_service_js_1 = require("./network-discovery-service.js");
const logger_js_1 = require("../utils/logger.js");
class HybridNetworkService extends events_1.EventEmitter {
    _config;
    _monitoringInterval = null;
    _isRunning = false;
    _healthCheckInterval = 30000; // 30 seconds
    _networkChangeDetectionInterval = 10000; // 10 seconds
    constructor(port = 8080) {
        super();
        this._config = {
            mode: 'hybrid',
            staticIP: null,
            dynamicIP: null,
            currentIP: 'localhost',
            port,
            isStaticAvailable: false,
            isDynamicAvailable: false,
            lastNetworkChange: new Date(),
            networkAdapter: null,
            subnetMask: null,
            gateway: null,
            dnsServers: []
        };
        logger_js_1.logger.info('🌐 Hybrid Network Service initialized');
    }
    /**
     * Start hybrid network management
     */
    async start() {
        try {
            if (this._isRunning) {
                logger_js_1.logger.warn('Hybrid Network Service is already running');
                return;
            }
            logger_js_1.logger.info('🚀 Starting Hybrid Network Management System...');
            // Phase 1: Analyze current network
            await this.analyzeCurrentNetwork();
            // Phase 2: Attempt static IP assignment
            await this.attemptStaticIPAssignment();
            // Phase 3: Initialize network discovery as backup
            await this.initializeNetworkDiscovery();
            // Phase 4: Start continuous monitoring
            this.startNetworkMonitoring();
            this._isRunning = true;
            logger_js_1.logger.info('✅ Hybrid Network Management System started successfully');
            logger_js_1.logger.info(`🖥️ Current Mode: ${this._config.mode}`);
            logger_js_1.logger.info(`📍 Active IP: ${this._config.currentIP}:${this._config.port}`);
            logger_js_1.logger.info(`🔧 Static IP Available: ${this._config.isStaticAvailable}`);
            logger_js_1.logger.info(`🔍 Dynamic Discovery Available: ${this._config.isDynamicAvailable}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to start Hybrid Network Service:', error);
            throw error;
        }
    }
    /**
     * Stop hybrid network management
     */
    async stop() {
        try {
            if (!this._isRunning) {
                return;
            }
            logger_js_1.logger.info('🛑 Stopping Hybrid Network Management System...');
            // Stop monitoring
            if (this._monitoringInterval) {
                clearInterval(this._monitoringInterval);
                this._monitoringInterval = null;
            }
            // Stop network discovery
            await network_discovery_service_js_1.networkDiscoveryService.stop();
            this._isRunning = false;
            logger_js_1.logger.info('✅ Hybrid Network Management System stopped');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error stopping Hybrid Network Service:', error);
        }
    }
    /**
     * Analyze current network configuration
     */
    async analyzeCurrentNetwork() {
        try {
            logger_js_1.logger.info('🔍 Analyzing current network configuration...');
            // Get current network information
            const networkInfo = await this.getCurrentNetworkInfo();
            this._config.dynamicIP = networkInfo.currentIP;
            this._config.currentIP = networkInfo.currentIP;
            this._config.networkAdapter = networkInfo.adapterName;
            this._config.subnetMask = networkInfo.subnetMask;
            this._config.gateway = networkInfo.gateway;
            this._config.dnsServers = networkInfo.dnsServers;
            this._config.isDynamicAvailable = true;
            logger_js_1.logger.info(`📍 Current IP: ${networkInfo.currentIP}`);
            logger_js_1.logger.info(`🔌 Network Adapter: ${networkInfo.adapterName}`);
            logger_js_1.logger.info(`🌐 Gateway: ${networkInfo.gateway}`);
            logger_js_1.logger.info(`🔍 Subnet: ${networkInfo.subnetMask}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error analyzing network:', error);
            throw error;
        }
    }
    /**
     * Attempt to assign static IP
     */
    async attemptStaticIPAssignment() {
        try {
            logger_js_1.logger.info('🔧 Static IP assignment disabled for development environment');
            logger_js_1.logger.info('📍 Using existing network configuration (dynamic mode)');

            // Skip static IP assignment entirely for development
            this._config.mode = 'dynamic';
            this._config.isStaticAvailable = false;

            // Get current network info without modifying it
            const networkInfo = await this.getCurrentNetworkInfo();
            if (networkInfo) {
                this._config.currentIP = networkInfo.currentIP;
                this._config.networkAdapter = networkInfo.adapterName;
                this._config.subnetMask = networkInfo.subnetMask;
                this._config.gateway = networkInfo.gateway;
                this._config.dnsServers = networkInfo.dnsServers;

                logger_js_1.logger.info(`📍 Using existing IP: ${networkInfo.currentIP}`);
                logger_js_1.logger.info(`🌐 Network adapter: ${networkInfo.adapterName}`);
            } else {
                // Fallback to localhost
                this._config.currentIP = 'localhost';
                logger_js_1.logger.info('📍 Using localhost fallback');
            }
        }
        catch (error) {
            logger_js_1.logger.warn('⚠️ Network info detection error - using localhost:', error);
            this._config.mode = 'dynamic';
            this._config.currentIP = 'localhost';
        }
    }
    /**
     * Initialize network discovery as backup
     */
    async initializeNetworkDiscovery() {
        try {
            logger_js_1.logger.info('🔍 Initializing network discovery backup system...');
            // Configure network discovery with current IP
            await network_discovery_service_js_1.networkDiscoveryService.start();
            this._config.isDynamicAvailable = true;
            logger_js_1.logger.info('✅ Network discovery backup system ready');
        }
        catch (error) {
            logger_js_1.logger.error('❌ Failed to initialize network discovery:', error);
            this._config.isDynamicAvailable = false;
        }
    }
    /**
     * Start continuous network monitoring
     */
    startNetworkMonitoring() {
        logger_js_1.logger.info('👁️ Starting continuous network monitoring...');
        this._monitoringInterval = setInterval(async () => {
            try {
                await this.performNetworkHealthCheck();
            }
            catch (error) {
                logger_js_1.logger.error('❌ Network health check error:', error);
            }
        }, this._healthCheckInterval);
        logger_js_1.logger.info(`⏰ Network monitoring active (${this._healthCheckInterval / 1000}s intervals)`);
    }
    /**
     * Perform network health check
     */
    async performNetworkHealthCheck() {
        try {
            const oldConfig = { ...this._config };
            let configChanged = false;
            // Check static IP availability
            if (this._config.staticIP) {
                const staticAvailable = await this.testIPConnectivity(this._config.staticIP);
                if (staticAvailable !== this._config.isStaticAvailable) {
                    this._config.isStaticAvailable = staticAvailable;
                    configChanged = true;
                    if (staticAvailable) {
                        logger_js_1.logger.info('✅ Static IP restored - switching back to static mode');
                        this._config.currentIP = this._config.staticIP;
                        this._config.mode = 'static';
                        this.emitNetworkChange('static_restored', oldConfig, this._config, 'Static IP connectivity restored');
                    }
                    else {
                        logger_js_1.logger.warn('⚠️ Static IP failed - switching to dynamic mode');
                        await this.switchToDynamicMode();
                        this.emitNetworkChange('static_failed', oldConfig, this._config, 'Static IP connectivity lost');
                    }
                }
            }
            // Check for network configuration changes
            const currentNetworkInfo = await this.getCurrentNetworkInfo();
            if (currentNetworkInfo.currentIP !== this._config.dynamicIP) {
                logger_js_1.logger.info(`🔄 Network change detected: ${this._config.dynamicIP} → ${currentNetworkInfo.currentIP}`);
                this._config.dynamicIP = currentNetworkInfo.currentIP;
                this._config.lastNetworkChange = new Date();
                configChanged = true;
                // If in dynamic mode, update current IP
                if (this._config.mode === 'dynamic') {
                    this._config.currentIP = currentNetworkInfo.currentIP;
                }
                this.emitNetworkChange('network_changed', oldConfig, this._config, 'Network configuration changed');
            }
            // Update network discovery if needed
            if (configChanged && this._config.isDynamicAvailable) {
                // Network discovery service will automatically pick up the new IP
                logger_js_1.logger.debug('🔄 Network discovery will adapt to changes automatically');
            }
        }
        catch (error) {
            logger_js_1.logger.error('❌ Network health check failed:', error);
        }
    }
    /**
     * Switch to dynamic mode
     */
    async switchToDynamicMode() {
        try {
            this._config.mode = 'dynamic';
            if (this._config.dynamicIP) {
                this._config.currentIP = this._config.dynamicIP;
            }
            else {
                const networkInfo = await this.getCurrentNetworkInfo();
                this._config.currentIP = networkInfo.currentIP;
                this._config.dynamicIP = networkInfo.currentIP;
            }
            logger_js_1.logger.info(`🔄 Switched to dynamic mode - using IP: ${this._config.currentIP}`);
        }
        catch (error) {
            logger_js_1.logger.error('❌ Error switching to dynamic mode:', error);
        }
    }
    /**
     * Test IP connectivity
     */
    async testIPConnectivity(ip) {
        try {
            // Simple ping test using Node.js
            const { exec } = await import('child_process');
            const { promisify } = await import('util');
            const execAsync = promisify(exec);
            const pingCommand = process.platform === 'win32'
                ? `ping -n 1 -w 1000 ${ip}`
                : `ping -c 1 -W 1 ${ip}`;
            await execAsync(pingCommand);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Emit network change event
     */
    emitNetworkChange(type, oldConfig, newConfig, reason) {
        const event = {
            type,
            oldConfig,
            newConfig,
            timestamp: new Date(),
            reason
        };
        this.emit('networkChange', event);
        logger_js_1.logger.info(`📡 Network change event: ${type} - ${reason}`);
    }
    /**
     * Get current network configuration
     */
    getNetworkConfiguration() {
        return { ...this._config };
    }
    /**
     * Get current server URL
     */
    getServerURL() {
        return `http://${this._config.currentIP}:${this._config.port}`;
    }
    /**
     * Check if service is running
     */
    isRunning() {
        return this._isRunning;
    }
    /**
     * Force switch to static mode (if available)
     */
    async switchToStaticMode() {
        if (!this._config.staticIP || !this._config.isStaticAvailable) {
            return false;
        }
        const oldConfig = { ...this._config };
        this._config.mode = 'static';
        this._config.currentIP = this._config.staticIP;
        this.emitNetworkChange('static_restored', oldConfig, this._config, 'Manual switch to static mode');
        return true;
    }
    /**
     * Force switch to dynamic mode
     */
    async switchToDynamicModeManual() {
        const oldConfig = { ...this._config };
        await this.switchToDynamicMode();
        this.emitNetworkChange('dynamic_activated', oldConfig, this._config, 'Manual switch to dynamic mode');
        return true;
    }
    // Integration with Static IP Assignment Service
    async getCurrentNetworkInfo() {
        const { staticIPAssignmentService } = await import('./static-ip-assignment-service.js');
        return await staticIPAssignmentService.getCurrentNetworkInfo();
    }
    async findOptimalStaticIP() {
        const { staticIPAssignmentService } = await import('./static-ip-assignment-service.js');
        return await staticIPAssignmentService.findOptimalStaticIP();
    }
    async assignStaticIP(config) {
        const { staticIPAssignmentService } = await import('./static-ip-assignment-service.js');
        const result = await staticIPAssignmentService.assignStaticIP(config);
        return result.success;
    }
}
exports.HybridNetworkService = HybridNetworkService;
// Export singleton instance
exports.hybridNetworkService = new HybridNetworkService();
//# sourceMappingURL=hybrid-network-service.js.map