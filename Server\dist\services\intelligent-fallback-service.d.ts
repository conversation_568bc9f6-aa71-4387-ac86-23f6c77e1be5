/**
 * Intelligent Fallback Service
 * Implements seamless switching between static IP and network discovery modes
 * Provides smart decision-making for network mode transitions
 */
import { EventEmitter } from 'events';
import { NetworkConfiguration, NetworkChangeEvent } from './hybrid-network-service.js';
export interface FallbackRule {
    id: string;
    name: string;
    condition: (config: NetworkConfiguration, event?: NetworkChangeEvent) => boolean;
    action: 'switch_to_static' | 'switch_to_dynamic' | 'maintain_current' | 'force_discovery';
    priority: number;
    cooldownMs: number;
    description: string;
}
export interface FallbackDecision {
    ruleId: string;
    ruleName: string;
    action: string;
    reason: string;
    confidence: number;
    timestamp: Date;
    networkConfig: NetworkConfiguration;
}
export interface FallbackStats {
    totalDecisions: number;
    staticToDynamic: number;
    dynamicToStatic: number;
    discoveryActivations: number;
    lastDecision: FallbackDecision | null;
    averageDecisionTime: number;
    successRate: number;
}
export declare class IntelligentFallbackService extends EventEmitter {
    private _isRunning;
    private _fallbackRules;
    private _decisionHistory;
    private _ruleCooldowns;
    private _stats;
    constructor();
    /**
     * Start intelligent fallback system
     */
    start(): Promise<void>;
    /**
     * Stop intelligent fallback system
     */
    stop(): Promise<void>;
    /**
     * Initialize default fallback rules
     */
    private initializeDefaultRules;
    /**
     * Handle network change events
     */
    private handleNetworkChange;
    /**
     * Evaluate network change and make fallback decision
     */
    private evaluateNetworkChange;
    /**
     * Execute fallback decision
     */
    private executeDecision;
    /**
     * Force network discovery restart
     */
    private forceNetworkDiscovery;
    /**
     * Start periodic evaluation
     */
    private startPeriodicEvaluation;
    /**
     * Evaluate periodic conditions
     */
    private evaluatePeriodicConditions;
    /**
     * Check if rule is applicable
     */
    private isRuleApplicable;
    /**
     * Check if rule is in cooldown
     */
    private isRuleInCooldown;
    /**
     * Calculate decision confidence
     */
    private calculateDecisionConfidence;
    /**
     * Check if network is stable
     */
    private isNetworkStable;
    /**
     * Update statistics
     */
    private updateStats;
    /**
     * Get fallback statistics
     */
    getStats(): FallbackStats;
    /**
     * Get decision history
     */
    getDecisionHistory(limit?: number): FallbackDecision[];
    /**
     * Add custom fallback rule
     */
    addCustomRule(rule: FallbackRule): void;
    /**
     * Remove fallback rule
     */
    removeRule(ruleId: string): boolean;
    /**
     * Check if service is running
     */
    isRunning(): boolean;
}
export declare const intelligentFallbackService: IntelligentFallbackService;
