/**
 * Static IP Assignment Service
 * Handles automated static IP assignment with network analysis and Windows configuration
 * Provides intelligent IP selection and conflict resolution
 */
export interface NetworkAdapter {
    name: string;
    description: string;
    physicalAddress: string;
    currentIP: string;
    subnetMask: string;
    gateway: string;
    dnsServers: string[];
    dhcpEnabled: boolean;
    isActive: boolean;
    interfaceIndex: number;
}
export interface NetworkAnalysis {
    adapters: NetworkAdapter[];
    primaryAdapter: NetworkAdapter | null;
    networkRange: {
        network: string;
        subnetMask: string;
        gateway: string;
        broadcastAddress: string;
        availableRange: {
            start: string;
            end: string;
        };
    } | null;
    recommendedStaticIPs: string[];
    conflictingIPs: string[];
}
export interface StaticIPAssignmentResult {
    success: boolean;
    assignedIP: string | null;
    adapterName: string | null;
    previousConfig: any;
    error: string | null;
    rollbackAvailable: boolean;
}
export declare class StaticIPAssignmentService {
    private _isWindows;
    private _backupConfigs;
    /**
     * Analyze current network configuration
     */
    analyzeNetwork(): Promise<NetworkAnalysis>;
    /**
     * Find optimal static IP configuration
     */
    findOptimalStaticIP(): Promise<{
        targetIP: string;
        subnetMask: string;
        gateway: string;
        dnsServers: string[];
        adapterName: string;
    } | null>;
    /**
     * Assign static IP to network adapter
     */
    assignStaticIP(config: {
        targetIP: string;
        subnetMask: string;
        gateway: string;
        dnsServers: string[];
        adapterName: string;
    }): Promise<StaticIPAssignmentResult>;
    /**
     * Get all network adapters
     */
    private getNetworkAdapters;
    /**
     * Find primary network adapter
     */
    private findPrimaryAdapter;
    /**
     * Analyze network range for static IP recommendations
     */
    private analyzeNetworkRange;
    /**
     * Find recommended static IP addresses
     */
    private findRecommendedStaticIPs;
    /**
     * Find conflicting IP addresses
     */
    private findConflictingIPs;
    /**
     * Select best static IP from recommendations
     */
    private selectBestStaticIP;
    /**
     * Test IP availability using ping
     */
    private testIPAvailability;
    /**
     * Backup adapter configuration
     */
    private backupAdapterConfiguration;
    /**
     * Set static IP configuration
     */
    private setStaticIPConfiguration;
    /**
     * Verify static IP assignment
     */
    private verifyStaticIPAssignment;
    /**
     * Rollback configuration
     */
    private rollbackConfiguration;
    /**
     * Convert subnet mask to CIDR notation
     */
    private cidrFromSubnetMask;
    /**
     * Get current network information
     */
    getCurrentNetworkInfo(): Promise<{
        currentIP: string;
        adapterName: string;
        subnetMask: string;
        gateway: string;
        dnsServers: string[];
    }>;
}
export declare const staticIPAssignmentService: StaticIPAssignmentService;
