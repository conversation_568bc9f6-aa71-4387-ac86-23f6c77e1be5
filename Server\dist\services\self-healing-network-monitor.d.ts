/**
 * Self-Healing Network Monitor
 * Provides continuous network monitoring with automatic adaptation to changes
 * Implements proactive network health management and recovery
 */
import { EventEmitter } from 'events';
export interface NetworkHealthMetrics {
    timestamp: Date;
    connectivity: {
        staticIPReachable: boolean;
        dynamicIPReachable: boolean;
        internetReachable: boolean;
        gatewayReachable: boolean;
        dnsResolution: boolean;
    };
    performance: {
        latency: number;
        packetLoss: number;
        bandwidth: number;
        responseTime: number;
    };
    stability: {
        ipChanges: number;
        connectionDrops: number;
        recoveryTime: number;
        uptime: number;
    };
    services: {
        vmsServerHealthy: boolean;
        networkDiscoveryActive: boolean;
        fallbackSystemActive: boolean;
    };
}
export interface HealingAction {
    id: string;
    type: 'restart_service' | 'switch_mode' | 'reset_adapter' | 'flush_dns' | 'force_discovery';
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    executedAt: Date;
    success: boolean;
    error?: string;
    metrics: NetworkHealthMetrics;
}
export interface MonitoringConfig {
    healthCheckInterval: number;
    connectivityTimeout: number;
    performanceThresholds: {
        maxLatency: number;
        maxPacketLoss: number;
        minBandwidth: number;
        maxResponseTime: number;
    };
    healingThresholds: {
        consecutiveFailures: number;
        criticalLatency: number;
        criticalPacketLoss: number;
    };
    enabled: boolean;
}
export declare class SelfHealingNetworkMonitor extends EventEmitter {
    private _isRunning;
    private _monitoringInterval;
    private _healthHistory;
    private _healingHistory;
    private _consecutiveFailures;
    private _lastHealthCheck;
    private _startTime;
    private _config;
    constructor();
    /**
     * Start network monitoring
     */
    start(): Promise<void>;
    /**
     * Stop network monitoring
     */
    stop(): Promise<void>;
    /**
     * Perform comprehensive health check
     */
    performHealthCheck(): Promise<void>;
    /**
     * Collect comprehensive health metrics
     */
    private collectHealthMetrics;
    /**
     * Test network connectivity
     */
    private testConnectivity;
    /**
     * Measure network performance
     */
    private measurePerformance;
    /**
     * Calculate stability metrics
     */
    private calculateStabilityMetrics;
    /**
     * Check service health
     */
    private checkServiceHealth;
    /**
     * Analyze health metrics and determine healing needs
     */
    private analyzeHealthMetrics;
    /**
     * Perform healing actions
     */
    private performHealingActions;
    /**
     * Get appropriate healing action for issue
     */
    private getHealingAction;
    /**
     * Execute healing action
     */
    private executeHealingAction;
    /**
     * Perform emergency healing when health checks consistently fail
     */
    private performEmergencyHealing;
    private restartServices;
    private switchNetworkMode;
    private resetNetworkAdapter;
    private flushDNSCache;
    private forceNetworkDiscovery;
    private pingHost;
    private measureLatency;
    private measurePacketLoss;
    private testDNSResolution;
    private isVMSServerHealthy;
    /**
     * Get monitoring statistics
     */
    getStats(): any;
    /**
     * Update monitoring configuration
     */
    updateConfig(newConfig: Partial<MonitoringConfig>): void;
    /**
     * Check if monitor is running
     */
    isRunning(): boolean;
}
export declare const selfHealingNetworkMonitor: SelfHealingNetworkMonitor;
