using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace VMSClient
{
    /// <summary>
    /// VMS Client Main Window
    /// Desktop client with automatic VMS server discovery
    /// </summary>
    public partial class MainWindow : Window
    {
        private NetworkDiscoveryClient _networkDiscovery;
        private string _vmsServerURL;
        private bool _isConnecting = false;
        private DispatcherTimer _discoveryTimer;

        public MainWindow()
        {
            InitializeComponent();
            InitializeVMSClient();
        }

        /// <summary>
        /// Initialize VMS Client
        /// </summary>
        private async void InitializeVMSClient()
        {
            try
            {
                LogStatus("🔧 Initializing VMS Client...", "Starting VMS client");
                UpdateProgress(10);

                // Initialize network discovery
                _networkDiscovery = new NetworkDiscoveryClient();
                _networkDiscovery.ServerDiscovered += OnServerDiscovered;
                _networkDiscovery.LogMessage += OnLogMessage;

                LogStatus("🔍 Starting server discovery...", "Scanning network for VMS server");
                UpdateProgress(30);

                // Start network discovery
                await _networkDiscovery.StartDiscoveryAsync();

                LogStatus("📡 Network discovery active", "Searching for VMS server");
                UpdateProgress(50);

                // Start periodic discovery updates
                StartPeriodicDiscovery();

                LogStatus("✅ VMS Client ready", "Waiting for VMS server detection");
                UpdateProgress(100);

            }
            catch (Exception ex)
            {
                LogError($"❌ Initialization failed: {ex.Message}");
                ShowErrorDialog("VMS Client Initialization Error",
                    $"Failed to initialize VMS Client:\n\n{ex.Message}\n\nPlease check your network connection and try again.");
            }
        }

        /// <summary>
        /// Handle server discovery events
        /// </summary>
        private async void OnServerDiscovered(object sender, ServerDiscoveredEventArgs e)
        {
            try
            {
                await Dispatcher.InvokeAsync(() =>
                {
                    var server = e.Server;
                    _vmsServerURL = $"http://{server.ServerIP}:{server.ServerPort}";

                    LogStatus("✅ VMS server detected!", $"Found server at {server.ServerIP}:{server.ServerPort}");
                    
                    NetworkStatusText.Text = "✅ VMS server found";
                    ServerInfoText.Text = $"Server: {server.ServerIP}:{server.ServerPort} ({server.Version})";
                    
                    ConnectButton.IsEnabled = true;
                    FooterStatusText.Text = "VMS server found - ready to connect";
                });
            }
            catch (Exception ex)
            {
                LogError($"Error handling server discovery: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle network discovery log messages
        /// </summary>
        private void OnLogMessage(object sender, string message)
        {
            Dispatcher.InvokeAsync(() =>
            {
                // Update detail text with latest discovery message
                if (message.Contains("Discovery request sent"))
                {
                    DetailText.Text = "Scanning network for VMS server...";
                }
                else if (message.Contains("Server announcement received"))
                {
                    DetailText.Text = "VMS server detected - preparing connection...";
                }
                else if (message.Contains("accessible and healthy"))
                {
                    DetailText.Text = "VMS server verified - ready to connect";
                }
            });
        }

        /// <summary>
        /// Start periodic discovery updates
        /// </summary>
        private void StartPeriodicDiscovery()
        {
            _discoveryTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(30) // Check every 30 seconds
            };
            
            _discoveryTimer.Tick += async (sender, e) =>
            {
                if (!_isConnecting && _networkDiscovery.IsRunning)
                {
                    // Refresh server list
                    var servers = _networkDiscovery.GetDiscoveredServers();
                    if (servers.Count == 0)
                    {
                        NetworkStatusText.Text = "🔍 Searching for VMS server...";
                        ServerInfoText.Text = "Server: Not detected";
                        ConnectButton.IsEnabled = false;
                    }
                }
            };
            
            _discoveryTimer.Start();
        }

        /// <summary>
        /// Connect to VMS server
        /// </summary>
        private async void ConnectButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_vmsServerURL))
            {
                ShowErrorDialog("Connection Error", "No VMS server detected. Please wait for server discovery or check your network connection.");
                return;
            }

            try
            {
                _isConnecting = true;
                ConnectButton.IsEnabled = false;
                
                LogStatus("🌐 Connecting to VMS server...", "Opening VMS application in browser");
                UpdateProgress(0);

                // Test connection
                UpdateProgress(30);
                
                LogStatus("✅ Connection verified", "Opening VMS in default browser");
                UpdateProgress(80);
                
                // Open browser to VMS server
                Process.Start(new ProcessStartInfo(_vmsServerURL)
                {
                    UseShellExecute = true
                });
                
                UpdateProgress(100);
                LogStatus("🎉 VMS opened successfully", "VMS application launched in browser");
                FooterStatusText.Text = "VMS opened in browser - you can close this window";
                
                // Auto-close after 3 seconds
                await Task.Delay(3000);
                Application.Current.Shutdown();

            }
            catch (Exception ex)
            {
                LogError($"❌ Connection failed: {ex.Message}");
                ShowErrorDialog("Connection Error", 
                    $"Failed to connect to VMS server:\n\n{ex.Message}\n\nPlease ensure the VMS server is running and try again.");
            }
            finally
            {
                _isConnecting = false;
                ConnectButton.IsEnabled = !string.IsNullOrEmpty(_vmsServerURL);
            }
        }

        /// <summary>
        /// Refresh server discovery
        /// </summary>
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                RefreshButton.IsEnabled = false;
                LogStatus("🔄 Refreshing server discovery...", "Scanning network for VMS server");
                
                // Reset status
                NetworkStatusText.Text = "🔍 Scanning for VMS server...";
                ServerInfoText.Text = "Server: Scanning...";
                ConnectButton.IsEnabled = false;
                
                // Restart discovery
                _networkDiscovery?.StopDiscovery();
                await Task.Delay(1000);
                await _networkDiscovery?.StartDiscoveryAsync();
                
                LogStatus("📡 Discovery refreshed", "Searching for VMS server");
            }
            catch (Exception ex)
            {
                LogError($"❌ Refresh failed: {ex.Message}");
            }
            finally
            {
                RefreshButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// Log status message
        /// </summary>
        private void LogStatus(string status, string detail)
        {
            StatusText.Text = status;
            DetailText.Text = detail;
        }

        /// <summary>
        /// Log error message
        /// </summary>
        private void LogError(string error)
        {
            StatusText.Text = error;
            StatusText.Foreground = System.Windows.Media.Brushes.Red;
            DetailText.Text = "Check network connection and VMS server status";
        }

        /// <summary>
        /// Update progress bar
        /// </summary>
        private void UpdateProgress(double value)
        {
            ProgressBar.Value = value;
        }

        /// <summary>
        /// Show error dialog
        /// </summary>
        private void ShowErrorDialog(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// Window closing event
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                _discoveryTimer?.Stop();
                _networkDiscovery?.StopDiscovery();
            }
            catch { }
            
            base.OnClosed(e);
        }
    }
}
