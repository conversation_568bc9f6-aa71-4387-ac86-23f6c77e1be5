# VMS CLIENT - Simple PowerShell Version
# For development machines without static IP

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# Create main form
$form = New-Object System.Windows.Forms.Form
$form.Text = "VMS CLIENT - Development"
$form.Size = New-Object System.Drawing.Size(500, 350)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = "FixedDialog"
$form.MaximizeBox = $false
$form.BackColor = [System.Drawing.Color]::FromArgb(248, 249, 250)

# Title Label
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Text = "VMS CLIENT"
$titleLabel.Location = New-Object System.Drawing.Point(20, 20)
$titleLabel.Size = New-Object System.Drawing.Size(450, 40)
$titleLabel.Font = New-Object System.Drawing.Font("Segoe UI", 18, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = [System.Drawing.Color]::FromArgb(21, 101, 192)
$titleLabel.TextAlign = "MiddleCenter"
$form.Controls.Add($titleLabel)

# Status Label
$statusLabel = New-Object System.Windows.Forms.Label
$statusLabel.Text = "Searching for VMS server..."
$statusLabel.Location = New-Object System.Drawing.Point(20, 80)
$statusLabel.Size = New-Object System.Drawing.Size(450, 30)
$statusLabel.Font = New-Object System.Drawing.Font("Segoe UI", 12, [System.Drawing.FontStyle]::Bold)
$statusLabel.ForeColor = [System.Drawing.Color]::Orange
$statusLabel.TextAlign = "MiddleCenter"
$form.Controls.Add($statusLabel)

# Server Info Label
$serverLabel = New-Object System.Windows.Forms.Label
$serverLabel.Text = "Server: Scanning..."
$serverLabel.Location = New-Object System.Drawing.Point(20, 120)
$serverLabel.Size = New-Object System.Drawing.Size(450, 25)
$serverLabel.Font = New-Object System.Drawing.Font("Segoe UI", 10)
$serverLabel.TextAlign = "MiddleCenter"
$form.Controls.Add($serverLabel)

# Progress Bar
$progressBar = New-Object System.Windows.Forms.ProgressBar
$progressBar.Location = New-Object System.Drawing.Point(50, 160)
$progressBar.Size = New-Object System.Drawing.Size(400, 20)
$progressBar.Style = "Marquee"
$form.Controls.Add($progressBar)

# Connect Button
$connectButton = New-Object System.Windows.Forms.Button
$connectButton.Text = "Connect to VMS"
$connectButton.Location = New-Object System.Drawing.Point(150, 200)
$connectButton.Size = New-Object System.Drawing.Size(200, 50)
$connectButton.Font = New-Object System.Drawing.Font("Segoe UI", 12, [System.Drawing.FontStyle]::Bold)
$connectButton.BackColor = [System.Drawing.Color]::FromArgb(189, 189, 189)
$connectButton.ForeColor = [System.Drawing.Color]::White
$connectButton.FlatStyle = "Flat"
$connectButton.Enabled = $false
$form.Controls.Add($connectButton)

# Refresh Button
$refreshButton = New-Object System.Windows.Forms.Button
$refreshButton.Text = "Refresh"
$refreshButton.Location = New-Object System.Drawing.Point(200, 270)
$refreshButton.Size = New-Object System.Drawing.Size(100, 30)
$refreshButton.Font = New-Object System.Drawing.Font("Segoe UI", 10)
$refreshButton.BackColor = [System.Drawing.Color]::FromArgb(21, 101, 192)
$refreshButton.ForeColor = [System.Drawing.Color]::White
$refreshButton.FlatStyle = "Flat"
$form.Controls.Add($refreshButton)

# Global variables
$script:serverUrl = ""
$script:isScanning = $false

# Function to update status
function Update-Status {
    param($message, $color)
    $statusLabel.Text = $message
    $statusLabel.ForeColor = $color
    $form.Refresh()
}

# Function to test server URL
function Test-ServerUrl {
    param($url)
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 3 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            $content = $response.Content
            if ($content -match "VMS|Voucher|<!DOCTYPE html>" -or $url -match ":8080") {
                return $true
            }
        }
    }
    catch {
        # URL not accessible
    }
    return $false
}

# Function to scan for VMS server
function Start-ServerScan {
    if ($script:isScanning) { return }
    $script:isScanning = $true
    
    Update-Status "Searching for VMS server..." ([System.Drawing.Color]::Orange)
    $serverLabel.Text = "Server: Scanning..."
    $connectButton.Enabled = $false
    $connectButton.BackColor = [System.Drawing.Color]::FromArgb(189, 189, 189)
    $connectButton.Text = "Connect to VMS"
    $progressBar.Style = "Marquee"
    
    # Test URLs in order of likelihood
    $testUrls = @(
        "http://localhost:8080",
        "http://127.0.0.1:8080",
        "http://localhost:3000",
        "http://localhost:8081",
        "http://localhost:9000"
    )
    
    foreach ($url in $testUrls) {
        Update-Status "Testing $url..." ([System.Drawing.Color]::Orange)
        if (Test-ServerUrl $url) {
            $script:serverUrl = $url
            Update-Status "VMS server found!" ([System.Drawing.Color]::Green)
            $serverLabel.Text = "Server: $url"
            $connectButton.Enabled = $true
            $connectButton.BackColor = [System.Drawing.Color]::FromArgb(76, 175, 80)
            $connectButton.Text = "Connect to VMS"
            $progressBar.Style = "Blocks"
            $progressBar.Value = 100
            $script:isScanning = $false
            return
        }
        Start-Sleep -Milliseconds 500
    }
    
    # Try network scan if localhost fails
    Update-Status "Scanning local network..." ([System.Drawing.Color]::Orange)
    
    try {
        # Get local IP
        $localIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -match "^192\.168\.|^10\.|^172\." } | Select-Object -First 1).IPAddress
        
        if ($localIP) {
            $subnet = $localIP.Substring(0, $localIP.LastIndexOf('.'))
            
            # Test a few IPs in the subnet
            for ($i = 1; $i -le 10; $i++) {
                $testIP = "$subnet.$i"
                $testUrl = "http://${testIP}:8080"
                
                Update-Status "Testing $testUrl..." ([System.Drawing.Color]::Orange)
                if (Test-ServerUrl $testUrl) {
                    $script:serverUrl = $testUrl
                    Update-Status "VMS server found on network!" ([System.Drawing.Color]::Green)
                    $serverLabel.Text = "Server: $testUrl"
                    $connectButton.Enabled = $true
                    $connectButton.BackColor = [System.Drawing.Color]::FromArgb(76, 175, 80)
                    $connectButton.Text = "Connect to VMS"
                    $progressBar.Style = "Blocks"
                    $progressBar.Value = 100
                    $script:isScanning = $false
                    return
                }
            }
        }
    }
    catch {
        # Network scan failed
    }
    
    # No server found
    Update-Status "No VMS server found" ([System.Drawing.Color]::Red)
    $serverLabel.Text = "Server: Not found - Make sure VMS server is running"
    $progressBar.Style = "Blocks"
    $progressBar.Value = 0
    $script:isScanning = $false
}

# Connect button click event
$connectButton.Add_Click({
    if ([string]::IsNullOrEmpty($script:serverUrl)) {
        [System.Windows.Forms.MessageBox]::Show("No VMS server detected. Please try refreshing.", "Connection Error", "OK", "Warning")
        return
    }
    
    try {
        Update-Status "Opening VMS system..." ([System.Drawing.Color]::Blue)
        Start-Process $script:serverUrl
        
        [System.Windows.Forms.MessageBox]::Show("VMS system opened successfully!`n`nServer: $($script:serverUrl)`n`nThe VMS CLIENT will now close.", "VMS CLIENT - Success", "OK", "Information")
        $form.Close()
    }
    catch {
        [System.Windows.Forms.MessageBox]::Show("Failed to open VMS system:`n`n$($_.Exception.Message)", "Connection Error", "OK", "Error")
        Update-Status "Failed to open browser" ([System.Drawing.Color]::Red)
    }
})

# Refresh button click event
$refreshButton.Add_Click({
    Start-ServerScan
})

# Start initial scan when form loads
$form.Add_Shown({
    Start-ServerScan
})

# Show the form
[System.Windows.Forms.Application]::Run($form)
