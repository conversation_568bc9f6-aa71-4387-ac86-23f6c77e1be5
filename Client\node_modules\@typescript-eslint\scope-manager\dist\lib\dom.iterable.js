"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.dom_iterable = void 0;
const base_config_1 = require("./base-config");
exports.dom_iterable = {
    AbortSignal: base_config_1.TYPE,
    AudioParam: base_config_1.TYPE,
    AudioParamMap: base_config_1.TYPE,
    BaseAudioContext: base_config_1.TYPE,
    Cache: base_config_1.TYPE,
    CanvasPath: base_config_1.TYPE,
    CanvasPathDrawingStyles: base_config_1.TYPE,
    CSSKeyframesRule: base_config_1.TYPE,
    CSSNumericArray: base_config_1.TYPE,
    CSSRuleList: base_config_1.TYPE,
    CSSStyleDeclaration: base_config_1.TYPE,
    CSSTransformValue: base_config_1.TYPE,
    CSSUnparsedValue: base_config_1.TYPE,
    CustomStateSet: base_config_1.TYPE,
    DataTransferItemList: base_config_1.TYPE,
    DOMRectList: base_config_1.TYPE,
    DOMStringList: base_config_1.TYPE,
    DOMTokenList: base_config_1.TYPE,
    EventCounts: base_config_1.TYPE,
    FileList: base_config_1.TYPE,
    FontFaceSet: base_config_1.TYPE,
    FormData: base_config_1.TYPE,
    FormDataIterator: base_config_1.TYPE,
    Headers: base_config_1.TYPE,
    HeadersIterator: base_config_1.TYPE,
    Highlight: base_config_1.TYPE,
    HighlightRegistry: base_config_1.TYPE,
    HTMLAllCollection: base_config_1.TYPE,
    HTMLCollectionBase: base_config_1.TYPE,
    HTMLCollectionOf: base_config_1.TYPE,
    HTMLFormElement: base_config_1.TYPE,
    HTMLSelectElement: base_config_1.TYPE,
    IDBDatabase: base_config_1.TYPE,
    IDBObjectStore: base_config_1.TYPE,
    MediaKeyStatusMap: base_config_1.TYPE,
    MediaKeyStatusMapIterator: base_config_1.TYPE,
    MediaList: base_config_1.TYPE,
    MessageEvent: base_config_1.TYPE,
    MIDIInputMap: base_config_1.TYPE,
    MIDIOutput: base_config_1.TYPE,
    MIDIOutputMap: base_config_1.TYPE,
    MimeTypeArray: base_config_1.TYPE,
    NamedNodeMap: base_config_1.TYPE,
    Navigator: base_config_1.TYPE,
    NodeList: base_config_1.TYPE,
    NodeListOf: base_config_1.TYPE,
    Plugin: base_config_1.TYPE,
    PluginArray: base_config_1.TYPE,
    RTCRtpTransceiver: base_config_1.TYPE,
    RTCStatsReport: base_config_1.TYPE,
    SourceBufferList: base_config_1.TYPE,
    SpeechRecognitionResult: base_config_1.TYPE,
    SpeechRecognitionResultList: base_config_1.TYPE,
    StylePropertyMapReadOnly: base_config_1.TYPE,
    StylePropertyMapReadOnlyIterator: base_config_1.TYPE,
    StyleSheetList: base_config_1.TYPE,
    SubtleCrypto: base_config_1.TYPE,
    SVGLengthList: base_config_1.TYPE,
    SVGNumberList: base_config_1.TYPE,
    SVGPointList: base_config_1.TYPE,
    SVGStringList: base_config_1.TYPE,
    SVGTransformList: base_config_1.TYPE,
    TextTrackCueList: base_config_1.TYPE,
    TextTrackList: base_config_1.TYPE,
    TouchList: base_config_1.TYPE,
    URLSearchParams: base_config_1.TYPE,
    URLSearchParamsIterator: base_config_1.TYPE,
    WEBGL_draw_buffers: base_config_1.TYPE,
    WEBGL_multi_draw: base_config_1.TYPE,
    WebGL2RenderingContextBase: base_config_1.TYPE,
    WebGL2RenderingContextOverloads: base_config_1.TYPE,
    WebGLRenderingContextBase: base_config_1.TYPE,
    WebGLRenderingContextOverloads: base_config_1.TYPE,
};
//# sourceMappingURL=dom.iterable.js.map